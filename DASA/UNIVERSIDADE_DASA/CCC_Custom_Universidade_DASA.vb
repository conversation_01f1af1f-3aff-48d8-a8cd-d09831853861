Public Sub CCC_Custom_Universidade_DASA(ByVal identificacao As String)
	Dim f = Session.SqlFormatter
    Dim loginName = identificacao
	Dim uidUD = "a3831ee8-11ae-430f-81c2-1eaeb89fd7f9"
	Dim personManagerAdsAccountQuery2 = f.AndRelation(f.UidComparison("AccountName", loginName), f.<PERSON><PERSON><PERSON>("UID_UNSRootB", uidUD))
    Dim personManagerAdsAccountCol2 As IEntityCollection = Session.Source().GetCollection(Query.From("UNSAccountB").Where(personManagerAdsAccountQuery2).SelectNonLobs)

Dim personManagerAdsAccount3 As IEntity = personManagerAdsAccountCol2(0)

Dim cpf As String = personManagerAdsAccount3.GetValue("CCC_CustomProperty11").String 'CPF
Dim mail As String = personManagerAdsAccount3.GetValue("CCC_CustomProperty15").String 'E-Mail
Dim matriculaGestor As String = personManagerAdsAccount3.GetValue("CCC_CustomProperty07").String 'Matrícula do Gestor
Dim nomeGestor As String = personManagerAdsAccount3.GetValue("CCC_CustomProperty06").String 'Nome do Gestor
Dim cpfGestor As String = personManagerAdsAccount3.GetValue("CCC_CustomProperty29").String 'CPF do Gestor
Dim negocio As String = personManagerAdsAccount3.GetValue("CCC_CustomProperty09").String 'Negócio
Dim marca As String = personManagerAdsAccount3.GetValue("CCC_CustomProperty05").String 'Marca
Dim estabelecimento As String = personManagerAdsAccount3.GetValue("CCC_CustomProperty10").String 'Estabelecimento
Dim departamento As String = personManagerAdsAccount3.GetValue("CCC_CustomProperty03").String 'Departamento
Dim codDepartamento As String = personManagerAdsAccount3.GetValue("CCC_CustomProperty02").String 'Código Departamento
Dim status As String = "True" 'Status
Dim dataAdmissao As String = personManagerAdsAccount3.GetValue("CCC_CustomProperty04").String 'Data de Admissão
Dim dataNascimento As String = personManagerAdsAccount3.GetValue("CCC_CustomProperty14").String 'Data de Nascimento
Dim genero As String = personManagerAdsAccount3.GetValue("CCC_CustomProperty13").String 'Genero
Dim grupoEmpregador As String = personManagerAdsAccount3.GetValue("CCC_CustomProperty08").String 'Grupo Empregador
Dim nome As String = personManagerAdsAccount3.GetValue("DisplayName").String 'Nome Completo
Dim matricula As String = personManagerAdsAccount3.GetValue("CCC_CustomProperty01").String 'Matricula
Dim segmento As String = "Funcionario" 'Segmento
Dim subGrupo As String = personManagerAdsAccount3.GetValue("CCC_CustomProperty30").String 'Subgrupo Empregador
Dim cargo As String = personManagerAdsAccount3.GetValue("CCC_CustomProperty12").String 'Cargo

' Define the JSON data to send  
Dim jsonData As String = String.Format("{{ ""cpf"": ""{0}"", ""email"": ""{1}"", ""gestor_Matricula"": ""{2}"", ""gestor_Nome_Completo"": ""{3}"", ""gestor_Cpf"": ""{4}"", ""negocio"": ""{5}"", ""marca"": ""{6}"", ""estabelecimento_Regional"": ""{7}"", ""departamento"": ""{8}"", ""cod_Departamento"": ""{9}"", ""cargo"": ""{10}"", ""status"": true, ""data_Entrada"": ""{11}"", ""data_Nascimento"": ""{12}"", ""genero"": ""{13}"", ""grupo_Empregador"": ""{14}"", ""nome_Completo"": ""{15}"", ""matricula"": ""{16}"", ""segmento_Principal"": ""{17}"", ""subgrupo_Empregador"": ""{18}"", ""Login"": ""{19}""}}", cpf, mail, matriculaGestor, nomeGestor, cpfGestor, negocio, marca, estabelecimento, departamento, codDepartamento, cargo, dataAdmissao, dataNascimento, genero, grupoEmpregador, nome, matricula, segmento, subGrupo, LoginName) 
' Create a new WebClient object  
Dim client As New System.Net.WebClient()  
  
' Set the content type header to indicate JSON data  
client.Headers.Add("Content-Type", "application/json; charset=utf-8")
client.Headers.Add("apikey", "xO5WIY2THA3KArocd52OIvg8nO1KyIew")
client.Headers.Add("Accept-Encoding", "gzip, deflate, br")
client.Headers.Add("Accept", "application/json, text/json, text/x-json, text/javascript, application/xml, text/xml") 

' Send the POST request and get the response data  
Dim encoding As New System.Text.UTF8Encoding()  
Dim jsonDataBytes As Byte() = encoding.GetBytes(jsonData)  
Dim responseBytes As Byte() = client.UploadData("https://api.godigibee.io/pipeline/dasa/v1/api-idm-universidades-dasa", "POST", jsonDataBytes)  
'Dim responseBytes As Byte() = client.UploadData("https://api.godigibee.io/pipeline/dasa/v1/api-idm-universidades-dasa", "POST", System.Text.Encoding.ASCII.GetBytes(jsonData))  
Dim responseString As String = System.Text.Encoding.ASCII.GetString(responseBytes)  

End Sub
