Imports Newtonsoft.Json.Linq
Imports Newtonsoft.Json
Imports System.Net
Imports VI.DB.Entities
Imports VI.DB.Specialized

Public Function CCC_RevvoLearning_Auth(ByVal UID_TSBAccountDef As String) As String
    Console.WriteLine("[DEBUG] CCC_RevvoLearning_Auth - INÍCIO - UID_TSBAccountDef: " & UID_TSBAccountDef)
    Dim accessToken As String = Nothing ' Variável para armazenar o token de acesso

    ' Validação de entrada crítica
    If String.IsNullOrWhiteSpace(UID_TSBAccountDef) Then
        Console.WriteLine("[ERRO CRÍTICO] UID_TSBAccountDef está vazio ou nulo")
        Return Nothing
    End If

    Try
        Console.WriteLine("[DEBUG] Iniciando obtenção de endpoints...")
        ' Obter endpoints baseados no AccountDef
        Dim endpoints As Dictionary(Of String, String) = CCC_RevvoLearning_GetEndpoints(UID_TSBAccountDef)

        If endpoints Is Nothing Then
            Console.WriteLine("[ERRO CRÍTICO] Função CCC_RevvoLearning_GetEndpoints retornou Nothing")
            Return Nothing
        End If

        If endpoints.Count = 0 Then
            Console.WriteLine("[ERRO CRÍTICO] Nenhum endpoint configurado para o AccountDef fornecido: " & UID_TSBAccountDef)
            Return Nothing
        End If

        Console.WriteLine("[DEBUG] Endpoints obtidos com sucesso. Total de configurações: " & endpoints.Count.ToString())

        ' Validação crítica dos parâmetros obrigatórios
        If Not endpoints.ContainsKey("ClientId") OrElse String.IsNullOrWhiteSpace(endpoints("ClientId")) Then
            Console.WriteLine("[ERRO CRÍTICO] ClientId não encontrado ou está vazio")
            Return Nothing
        End If

        If Not endpoints.ContainsKey("ClientSecret") OrElse String.IsNullOrWhiteSpace(endpoints("ClientSecret")) Then
            Console.WriteLine("[ERRO CRÍTICO] ClientSecret não encontrado ou está vazio")
            Return Nothing
        End If

        If Not endpoints.ContainsKey("TokenURL") OrElse String.IsNullOrWhiteSpace(endpoints("TokenURL")) Then
            Console.WriteLine("[ERRO CRÍTICO] TokenURL não encontrado ou está vazio")
            Return Nothing
        End If

        Dim clientId As String = endpoints("ClientId")
        Dim clientSecret As String = endpoints("ClientSecret")
        Dim tokenURL As String = endpoints("TokenURL")

        Console.WriteLine("[DEBUG] Usando ambiente: " & endpoints("Environment") & " - TokenURL: " & tokenURL)
        Console.WriteLine("[DEBUG] ClientId: " & clientId.Substring(0, Math.Min(4, clientId.Length)) & "***") ' Log parcial por segurança

        Console.WriteLine("[DEBUG] Iniciando requisição HTTP...")

        ' Declarar variáveis no escopo correto para evitar problemas de acessibilidade
        Dim responseString As String = ""

        Using client As New WebClient()
            Try
                ' Define o Content-Type para indicar que o corpo da requisição é JSON
                client.Headers(HttpRequestHeader.ContentType) = "application/json"
                Console.WriteLine("[DEBUG] Content-Type definido como application/json")

                ' Prepara os dados JSON para a requisição de autenticação
                ' É crucial que a string JSON seja formatada corretamente com aspas duplas escapadas.
                Dim jsonData As String = String.Format(
                    "{{""grant_type"": ""client_credentials"", ""client_id"": ""{0}"", ""client_secret"": ""{1}""}}",
                    clientId, clientSecret
                )

                Console.WriteLine("[DEBUG] JSON preparado para requisição (ClientSecret omitido por segurança)")
                Console.WriteLine("[DEBUG] Tamanho do JSON: " & jsonData.Length.ToString() & " caracteres")

                ' Validação crítica do JSON antes do envio
                If String.IsNullOrWhiteSpace(jsonData) Then
                    Console.WriteLine("[ERRO CRÍTICO] JSON de requisição está vazio")
                    Return Nothing
                End If

                ' Converte a string JSON para um array de bytes usando UTF8, que é padrão para JSON
                Dim requestBytes As Byte() = System.Text.Encoding.UTF8.GetBytes(jsonData)
                Console.WriteLine("[DEBUG] JSON convertido para bytes. Tamanho: " & requestBytes.Length.ToString() & " bytes")

                ' Validação da URL antes da requisição
                If Not Uri.IsWellFormedUriString(tokenURL, UriKind.Absolute) Then
                    Console.WriteLine("[ERRO CRÍTICO] TokenURL não é uma URL válida: " & tokenURL)
                    Return Nothing
                End If

                Console.WriteLine("[DEBUG] Enviando requisição POST para: " & tokenURL)

                ' Realiza a requisição POST para o endpoint de token
                Dim responseBytes As Byte() = client.UploadData(tokenURL, "POST", requestBytes)
                Console.WriteLine("[DEBUG] Resposta recebida. Tamanho: " & responseBytes.Length.ToString() & " bytes")

                ' Validação crítica da resposta
                If responseBytes Is Nothing OrElse responseBytes.Length = 0 Then
                    Console.WriteLine("[ERRO CRÍTICO] Resposta do servidor está vazia ou nula")
                    Return Nothing
                End If

                ' Converte a resposta em bytes para uma string usando UTF8
                responseString = System.Text.Encoding.UTF8.GetString(responseBytes)
                Console.WriteLine("[DEBUG] Resposta convertida para string. Tamanho: " & responseString.Length.ToString() & " caracteres")

                ' Validação da resposta antes do parse JSON
                If String.IsNullOrWhiteSpace(responseString) Then
                    Console.WriteLine("[ERRO CRÍTICO] String de resposta está vazia após conversão")
                    Return Nothing
                End If

                Console.WriteLine("[DEBUG] Iniciando parse do JSON de resposta...")

                ' Desserializa a string JSON da resposta para um objeto JObject
                Dim jsonResponse As JObject = JObject.Parse(responseString)
                Console.WriteLine("[DEBUG] JSON parseado com sucesso")

                ' Verifica se o objeto JSON e o campo 'access_token' existem antes de tentar acessá-los
                If jsonResponse IsNot Nothing AndAlso jsonResponse("access_token") IsNot Nothing Then
                    accessToken = jsonResponse("access_token").ToString()
                    Console.WriteLine("[DEBUG] Token de acesso extraído com sucesso. Tamanho: " & accessToken.Length.ToString() & " caracteres")
                    Console.WriteLine("[SUCESSO] Token de acesso obtido com sucesso.")

                    ' Log adicional de informações do token se disponíveis
                    If jsonResponse("expires_in") IsNot Nothing Then
                        Console.WriteLine("[DEBUG] Token expira em: " & jsonResponse("expires_in").ToString() & " segundos")
                    End If

                    If jsonResponse("token_type") IsNot Nothing Then
                        Console.WriteLine("[DEBUG] Tipo do token: " & jsonResponse("token_type").ToString())
                    End If
                Else
                    Console.WriteLine("[ERRO CRÍTICO] Resposta JSON não contém 'access_token' ou está vazia.")
                    Console.WriteLine("[DEBUG] Resposta completa: " & responseString)

                    ' Log de campos disponíveis para debug
                    If jsonResponse IsNot Nothing Then
                        Console.WriteLine("[DEBUG] Campos disponíveis na resposta:")
                        For Each prop In jsonResponse.Properties()
                            Console.WriteLine("[DEBUG]   - " & prop.Name)
                        Next
                    End If
                    Return Nothing
                End If

            Catch jsonEx As JsonException
                Console.WriteLine("[ERRO CRÍTICO] Erro ao fazer parse do JSON de resposta: " & jsonEx.Message)
                If Not String.IsNullOrWhiteSpace(responseString) Then
                    Console.WriteLine("[DEBUG] Resposta que causou erro: " & responseString)
                Else
                    Console.WriteLine("[DEBUG] Resposta estava vazia ou nula quando ocorreu o erro de JSON")
                End If
                Return Nothing
            End Try

        End Using ' Garante que o WebClient seja descartado corretamente
        Console.WriteLine("[DEBUG] WebClient descartado corretamente")

    Catch ex As WebException
        ' Captura exceções específicas de requisições web (e.g., erros HTTP 4xx, 5xx)
        Console.WriteLine("[ERRO CRÍTICO] Erro na requisição web durante a autenticação: " & ex.Message)
        Console.WriteLine("[DEBUG] Status da WebException: " & ex.Status.ToString())

        If ex.Response IsNot Nothing Then
            Try
                Dim httpResponse As HttpWebResponse = CType(ex.Response, HttpWebResponse)
                Console.WriteLine("[DEBUG] Código de status HTTP: " & httpResponse.StatusCode.ToString() & " (" & CInt(httpResponse.StatusCode).ToString() & ")")
                Console.WriteLine("[DEBUG] Descrição do status: " & httpResponse.StatusDescription)

                Using reader As New System.IO.StreamReader(ex.Response.GetResponseStream())
                    Dim errorResponse As String = reader.ReadToEnd()
                    Console.WriteLine("[DEBUG] Resposta de erro do servidor: " & errorResponse)

                    ' Tentar fazer parse do JSON de erro se possível
                    If Not String.IsNullOrWhiteSpace(errorResponse) AndAlso errorResponse.Trim().StartsWith("{") Then
                        Try
                            Dim errorJson As JObject = JObject.Parse(errorResponse)
                            If errorJson("error") IsNot Nothing Then
                                Console.WriteLine("[DEBUG] Tipo de erro: " & errorJson("error").ToString())
                            End If
                            If errorJson("error_description") IsNot Nothing Then
                                Console.WriteLine("[DEBUG] Descrição do erro: " & errorJson("error_description").ToString())
                            End If
                        Catch jsonParseEx As Exception
                            Console.WriteLine("[DEBUG] Não foi possível fazer parse do JSON de erro: " & jsonParseEx.Message)
                        End Try
                    End If
                End Using
            Catch responseEx As Exception
                Console.WriteLine("[DEBUG] Erro ao processar resposta de erro: " & responseEx.Message)
            End Try
        Else
            Console.WriteLine("[DEBUG] Nenhuma resposta disponível na WebException")
        End If

        ' Em caso de erro, retorna Nothing para indicar falha na obtenção do token
        Console.WriteLine("[DEBUG] Retornando Nothing devido a WebException")
        Return Nothing

    Catch ex As Exception
        ' Captura outras exceções genéricas
        Console.WriteLine("[ERRO CRÍTICO] Ocorreu um erro inesperado durante a autenticação: " & ex.Message)
        Console.WriteLine("[DEBUG] Tipo da exceção: " & ex.GetType().Name)
        Console.WriteLine("[DEBUG] Stack trace: " & ex.StackTrace)

        ' Log de inner exception se existir
        If ex.InnerException IsNot Nothing Then
            Console.WriteLine("[DEBUG] Inner exception: " & ex.InnerException.Message)
            Console.WriteLine("[DEBUG] Inner exception type: " & ex.InnerException.GetType().Name)
        End If

        ' Em caso de erro, retorna Nothing
        Console.WriteLine("[DEBUG] Retornando Nothing devido a exceção genérica")
        Return Nothing
    End Try

    Console.WriteLine("[DEBUG] CCC_RevvoLearning_Auth - FIM - Token obtido: " & If(accessToken IsNot Nothing, "SIM", "NÃO"))
    Return accessToken ' Retorna o token de acesso (ou Nothing se houve erro)
End Function

' Função auxiliar para determinar URLs baseadas no AccountDef
Public Function CCC_RevvoLearning_GetEndpoints(ByVal UID_TSBAccountDef As String) As Dictionary(Of String, String)
    Console.WriteLine("[DEBUG] CCC_RevvoLearning_GetEndpoints - INÍCIO - UID_TSBAccountDef: " & UID_TSBAccountDef)
    Dim endpoints As New Dictionary(Of String, String)

    ' Validação de entrada crítica
    If String.IsNullOrWhiteSpace(UID_TSBAccountDef) Then
        Console.WriteLine("[ERRO CRÍTICO] UID_TSBAccountDef está vazio ou nulo na função GetEndpoints")
        Return endpoints ' Retorna dicionário vazio
    End If

    Try
        Dim AccountDef As String = UID_TSBAccountDef.Trim() ' Remove espaços em branco
        Console.WriteLine("[DEBUG] AccountDef processado: '" & AccountDef & "'")

        ' Definir mapeamento de ambientes
        Dim environments() As String = {"DASA", "AmericasFunc", "AmericasMed"}
        Console.WriteLine("[DEBUG] Ambientes configurados: " & String.Join(", ", environments))

        ' Buscar o ambiente correspondente
        For Each env As String In environments
            Console.WriteLine("[DEBUG] Verificando ambiente: " & env)

            Try
                Dim configPath As String = $"Custom\CustomTargetSystem\RevvoLearning\{env}\UIDAccountDef{env}"
                Console.WriteLine("[DEBUG] Buscando configuração em: " & configPath)

                Dim configAccountDef As String = Connection.GetConfigParm(configPath)
                Console.WriteLine("[DEBUG] Valor encontrado: '" & If(configAccountDef, "NULL") & "'")

                If String.IsNullOrWhiteSpace(configAccountDef) Then
                    Console.WriteLine("[AVISO] Configuração vazia para ambiente: " & env)
                    Continue For
                End If

                If AccountDef.Equals(configAccountDef.Trim(), StringComparison.OrdinalIgnoreCase) Then
                    Console.WriteLine("[DEBUG] Match encontrado para ambiente: " & env)

                    ' Buscar todas as configurações necessárias
                    Dim tokenURL As String = Connection.GetConfigParm($"Custom\CustomTargetSystem\RevvoLearning\{env}\TokenURL")
                    Dim url As String = Connection.GetConfigParm($"Custom\CustomTargetSystem\RevvoLearning\{env}\URL")
                    Dim clientId As String = Connection.GetConfigParm($"Custom\CustomTargetSystem\RevvoLearning\{env}\ClientId")
                    Dim clientSecret As String = Connection.GetConfigParm($"Custom\CustomTargetSystem\RevvoLearning\{env}\ClientSecret")

                    Console.WriteLine("[DEBUG] TokenURL: " & If(tokenURL, "NULL"))
                    Console.WriteLine("[DEBUG] URL: " & If(url, "NULL"))
                    Console.WriteLine("[DEBUG] ClientId: " & If(String.IsNullOrWhiteSpace(clientId), "NULL", clientId.Substring(0, Math.Min(4, clientId.Length)) & "***"))
                    Console.WriteLine("[DEBUG] ClientSecret: " & If(String.IsNullOrWhiteSpace(clientSecret), "NULL", "***DEFINIDO***"))

                    ' Validações críticas das configurações
                    If String.IsNullOrWhiteSpace(tokenURL) Then
                        Console.WriteLine("[ERRO CRÍTICO] TokenURL não configurado para ambiente: " & env)
                        Continue For
                    End If

                    If String.IsNullOrWhiteSpace(clientId) Then
                        Console.WriteLine("[ERRO CRÍTICO] ClientId não configurado para ambiente: " & env)
                        Continue For
                    End If

                    If String.IsNullOrWhiteSpace(clientSecret) Then
                        Console.WriteLine("[ERRO CRÍTICO] ClientSecret não configurado para ambiente: " & env)
                        Continue For
                    End If

                    ' Adicionar configurações ao dicionário
                    endpoints("TokenURL") = tokenURL.Trim()
                    endpoints("URL") = If(url, "").Trim()
                    endpoints("ClientId") = clientId.Trim()
                    endpoints("ClientSecret") = clientSecret.Trim()
                    endpoints("Environment") = env

                    Console.WriteLine("[SUCESSO] Ambiente encontrado e configurado: " & env)
                    Exit For
                End If

            Catch configEx As Exception
                Console.WriteLine("[ERRO] Erro ao buscar configuração para ambiente " & env & ": " & configEx.Message)
                Continue For
            End Try
        Next

        ' Verificar se encontrou algum ambiente
        If endpoints.Count = 0 Then
            Console.WriteLine("[ERRO CRÍTICO] AccountDef '" & AccountDef & "' não encontrado nos ambientes configurados")
            Console.WriteLine("[DEBUG] Listando ambientes disponíveis para diagnóstico:")

            For Each env As String In environments
                Try
                    Dim availableAccountDef As String = Connection.GetConfigParm($"Custom\CustomTargetSystem\RevvoLearning\{env}\UIDAccountDef{env}")
                    Console.WriteLine("[DEBUG]   - " & env & ": '" & If(availableAccountDef, "NULL") & "'")
                Catch envEx As Exception
                    Console.WriteLine("[DEBUG]   - " & env & ": ERRO ao buscar configuração - " & envEx.Message)
                End Try
            Next
        Else
            Console.WriteLine("[DEBUG] Configurações carregadas com sucesso. Total de endpoints: " & endpoints.Count.ToString())
        End If

    Catch ex As Exception
        Console.WriteLine("[ERRO CRÍTICO] Erro ao determinar endpoints: " & ex.Message)
        Console.WriteLine("[DEBUG] Tipo da exceção: " & ex.GetType().Name)
        Console.WriteLine("[DEBUG] Stack trace: " & ex.StackTrace)

        If ex.InnerException IsNot Nothing Then
            Console.WriteLine("[DEBUG] Inner exception: " & ex.InnerException.Message)
        End If

        ' Retorna dicionário vazio em caso de erro
        endpoints.Clear()
    End Try

    Console.WriteLine("[DEBUG] CCC_RevvoLearning_GetEndpoints - FIM - Endpoints encontrados: " & endpoints.Count.ToString())
    Return endpoints
End Function

