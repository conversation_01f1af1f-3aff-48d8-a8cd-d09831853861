#If Not SCRIPTDEBUGGER Then
References VI.DataImport.dll

Imports System.Collections.Generic
Imports System.IO
Imports System.Globalization
Imports VI.DB.Entities
Imports VI.DB.Specialized
Imports VI.DataImport
Imports Newtonsoft.Json.Linq
Imports Newtonsoft.Json
Imports System.Net
#End If

Public Function CCC_RevvoLearning_Disable(ByVal UID_UNSAccountB As String, ByVal UID_TSBAccountDef As String) As String
    Dim logMessage As String = ""

    Try
        logMessage = String.Format("[{0}] Iniciando desabilitação de usuário para UID: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), UID_UNSAccountB)
        Console.WriteLine(logMessage)

        Dim f = Session.SqlFormatter
        Dim queryUNSAccountB = f.UidComparison("UID_UNSAccountB", UID_UNSAccountB)
        Dim UNSAccountBCol As IEntityCollection = Session.Source().GetCollection(Query.From("UNSAccountB").Where(queryUNSAccountB).SelectNonLobs)

        If UNSAccountBCol.Count > 0 Then
            Dim UNSAccountB As IEntity = UNSAccountBCol(0)

            Dim externalUserId As String = UNSAccountB.GetValue("CCC_CustomProperty24").String
            
            If String.IsNullOrEmpty(externalUserId) Then
                logMessage = String.Format("[{0}] ERRO: ID externo não encontrado no campo CCC_CustomProperty24", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))
                Console.WriteLine(logMessage)
                Return "ERRO: ID externo não encontrado"
            End If

            logMessage = String.Format("[{0}] ID externo encontrado: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), externalUserId)
            Console.WriteLine(logMessage)

            logMessage = String.Format("[{0}] Obtendo token de autenticação...", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))
            Console.WriteLine(logMessage)

            Dim authToken As String = CCC_RevvoLearning_Auth(UID_TSBAccountDef)

            If String.IsNullOrEmpty(authToken) Then
                logMessage = String.Format("[{0}] ERRO: Falha na autenticação - Token não obtido", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))
                Console.WriteLine(logMessage)
                Return "ERRO: Falha na autenticação"
            End If

            logMessage = String.Format("[{0}] Token obtido com sucesso", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))
            Console.WriteLine(logMessage)
            
            Dim jsonData As String = "{""user"": {""suspended"": true}}"

            logMessage = String.Format("[{0}] Enviando requisição para desabilitar usuário na API RevvoLearning...", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))
            Console.WriteLine(logMessage)

            Try
                Using client As New WebClient()
                    client.Headers.Add("Content-Type", "application/json")
                    client.Headers.Add("Authorization", "Bearer " & authToken)

					Dim revvoURL As String = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\DASA\URL")
					
                    Dim responseBytes As Byte() = client.UploadData(revvoURL & "/" & externalUserId, "PATCH", System.Text.Encoding.UTF8.GetBytes(jsonData))
                    Dim responseString As String = System.Text.Encoding.UTF8.GetString(responseBytes)

                    logMessage = String.Format("[{0}] Resposta da API recebida: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), responseString)
                    Console.WriteLine(logMessage)

                    logMessage = String.Format("[{0}] Usuário desabilitado com sucesso! ID: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), externalUserId)
                    Console.WriteLine(logMessage)

                    Return "SUCESSO: Usuário desabilitado com ID " & externalUserId

                End Using
            Catch webEx As WebException
                Dim errorResponse As String = ""
                Dim statusCode As String = ""

                If webEx.Response IsNot Nothing Then
                    Dim httpResponse As HttpWebResponse = CType(webEx.Response, HttpWebResponse)
                    statusCode = httpResponse.StatusCode.ToString() & " (" & CInt(httpResponse.StatusCode) & ")"

                    Using reader As New StreamReader(webEx.Response.GetResponseStream())
                        errorResponse = reader.ReadToEnd()
                    End Using
                End If

                logMessage = String.Format("[{0}] ERRO na API: {1} | {2} | Resposta: {3}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), statusCode, webEx.Message, errorResponse)
                Console.WriteLine(logMessage)
                Return "ERRO: Falha na API - " & statusCode & " | " & webEx.Message & " | Resposta: " & errorResponse

            Catch ex As Exception
                logMessage = String.Format("[{0}] ERRO geral: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), ex.Message)
                Console.WriteLine(logMessage)
                Return "ERRO: " & ex.Message
            End Try

        Else
            logMessage = String.Format("[{0}] ERRO: Conta não encontrada para UID: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), UID_UNSAccountB)
            Console.WriteLine(logMessage)
            Return "ERRO: Conta não encontrada"
        End If

    Catch generalEx As Exception
        logMessage = String.Format("[{0}] ERRO CRÍTICO: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), generalEx.Message)
        Console.WriteLine(logMessage)
        Return "ERRO CRÍTICO: " & generalEx.Message
    End Try
End Function
