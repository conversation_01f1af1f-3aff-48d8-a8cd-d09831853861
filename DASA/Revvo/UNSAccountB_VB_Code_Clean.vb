' ========================================
' CÓDIGOS VB EXTRAÍDOS DO ARQUIVO UNSAccountB
' ========================================

' ========================================
' 3. AccountName - Template VB
' ========================================
If (Not CBool(Variables("FULLSYNC")) Or _
(CBool(Variables("FULLSYNC")) And ($FK(UID_Person).ImportSource$).StartsWith("SF_HR"))) Then

	'Set C+CPF or T+CPF as LogonName if TargetSystem is Universidade DASA (UD)
	If $FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\UniversidadeDASA\AccountDef") andAlso _
		$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\UniversidadeDASA") andAlso _
		$UID_TSBBehavior$ = "TSB-FullManaged" Then
		
		Value = $FK(UID_Person).CentralAccount$

	Else If $UID_UNSRootB$ = "e0a2b68f-aafe-4a8b-8119-15fc4007f068" Then
		
		Value = $FK(UID_Person).CentralAccount$
	
	' Verifica o K2
	Else If Not $[IsLoaded]:Bool$ and $FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\Gliese\AccountDef") andAlso _
		$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\Gliese\TargetSystemName") andAlso _
		$UID_TSBBehavior$ = "TSB-FullManaged" Then
		
    	Value = Left($FK(UID_Person).CentralAccount$, 10)
	
	' Verifica o Progress
	Else If Not $[IsLoaded]:Bool$ and (($FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\Progress\AccountDefSP")) OR _
		($FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\Progress\AccountDefRJ"))) andAlso _
		(($FK(UID_UNSRootB).Ident_UNSRoot$ = "ProgressRJ") OR ($FK(UID_UNSRootB).Ident_UNSRoot$ = "ProgressSP")) andAlso _
		$UID_TSBBehavior$ = "TSB-FullManaged" Then
		
		' Verifica se o campo esta preenchido
		Try
			If $CCC_CustomProperty21$ = "" Or CInt($CCC_CustomProperty21$) = 0 Then
				
				Value = $FK(UID_Person).CentralAccount$.Substring(0, 6)
				
			Else if CINT($CCC_CustomProperty21$) > 0 Then
				
				Value = $CCC_CustomProperty21$
			
			End If
			
		Catch ex As Exception
			
			Value = $FK(UID_Person).CentralAccount$.Substring(0, 6)
			
		End Try
		
	Else If $FK(UID_Person).IdentityType$ = "Primary" AndAlso _
		$FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\DASA\AccountDefDasa") AndAlso _
		$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning") AndAlso _
		$UID_TSBBehavior$ = "TSB-FullManaged" Then
	
		Value = $cn$

	Else If $FK(UID_Person).IdentityType$ = "Primary" AndAlso _
		$FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\AmericasFunc\AccountDefAmericasFunc") AndAlso _
		$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning") AndAlso _
		$UID_TSBBehavior$ = "TSB-FullManaged" Then
	
		Value = $cn$

	Else If $FK(UID_Person).IdentityType$ = "Primary" AndAlso _
		$FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\AmericasMed\AccountDefAmericasMed") AndAlso _
		$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning") AndAlso _
		$UID_TSBBehavior$ = "TSB-FullManaged" Then
	
		Value = $cn$
		
	Else If Not $[IsLoaded]:Bool$ Then
	
		value = $cn$
	
	End If
	
End If

' ========================================
' 8. CCC_CustomProperty03 - Template VB
' ========================================
If (Not CBool(Variables("FULLSYNC")) Or _
(CBool(Variables("FULLSYNC")) And ($FK(UID_Person).ImportSource$).StartsWith("SF_HR"))) Then

	If $FK(UID_Person).IdentityType$ = "Primary" AndAlso _
		$FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\UniversidadeDASA\AccountDef") AndAlso _
		$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\UniversidadeDASA") AndAlso _
		$UID_TSBBehavior$ = "TSB-FullManaged" Then
		
		Value = $FK(UID_Person).FK(UID_Department).ShortName$
		
	Else If $FK(UID_Person).IdentityType$ = "Primary" AndAlso _
		$FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\DASA\AccountDefDasa") AndAlso _
		$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning") AndAlso _
		$UID_TSBBehavior$ = "TSB-FullManaged" Then
	
		Value = $FK(UID_Person).FK(UID_Department).ShortName$

	Else If $FK(UID_Person).IdentityType$ = "Primary" AndAlso _
		$FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\AmericasFunc\AccountDefAmericasFunc") AndAlso _
		$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning") AndAlso _
		$UID_TSBBehavior$ = "TSB-FullManaged" Then

		Value = $FK(UID_Person).FK(UID_Department).ShortName$

	Else If $FK(UID_Person).IdentityType$ = "Primary" AndAlso _
		$FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\AmericasMed\AccountDefAmericasMed") AndAlso _
		$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning") AndAlso _
		$UID_TSBBehavior$ = "TSB-FullManaged" Then

		Value = $FK(UID_Person).FK(UID_Department).ShortName$

	End If
End If

' ========================================
' 8. CCC_CustomProperty04 - Template VB
' ========================================
If (Not CBool(Variables("FULLSYNC")) Or _
(CBool(Variables("FULLSYNC")) And ($FK(UID_Person).ImportSource$).StartsWith("SF_HR"))) Then

	If $FK(UID_Person).IdentityType$ = "Primary" AndAlso _
		$FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\UniversidadeDASA\AccountDef") AndAlso _
		$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\UniversidadeDASA") AndAlso _
		$UID_TSBBehavior$ = "TSB-FullManaged" AndAlso _
		Not String.IsNullOrEmpty($FK(UID_Person).EntryDate$) Then

		Dim dTime As Date = $FK(UID_Person).EntryDate:Date$

		Value = dTime.ToString("dd/MM/yyyy")

	Else If $FK(UID_Person).IdentityType$ = "Primary" AndAlso _
		$FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\UniversidadeDASA\AccountDef") AndAlso _
		$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\UniversidadeDASA") AndAlso _
		$UID_TSBBehavior$ = "TSB-FullManaged" AndAlso _
		$FK(UID_Person).IsExternal:bool$ Then

		Value = ""

	Else If $FK(UID_Person).IdentityType$ = "Primary" AndAlso _
		$FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\DASA\AccountDefDasa") AndAlso _
		$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning") AndAlso _
		$UID_TSBBehavior$ = "TSB-FullManaged" AndAlso _
		Not String.IsNullOrEmpty($FK(UID_Person).EntryDate$) Then

		Dim dTime As Date = $FK(UID_Person).EntryDate:Date$

		Dim unixEpoch As Date = New Date(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc)
		Dim timestamp As Long = CLng((dTime.ToUniversalTime() - unixEpoch).TotalSeconds)

		Value = timestamp.ToString()

	Else If $FK(UID_Person).IdentityType$ = "Primary" AndAlso _
		$FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\AmericasFunc\AccountDefAmericasFunc") AndAlso _
		$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning") AndAlso _
		$UID_TSBBehavior$ = "TSB-FullManaged" AndAlso _
		Not String.IsNullOrEmpty($FK(UID_Person).EntryDate$) Then

		Dim dTime As Date = $FK(UID_Person).EntryDate:Date$

		Dim unixEpoch As Date = New Date(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc)
		Dim timestamp As Long = CLng((dTime.ToUniversalTime() - unixEpoch).TotalSeconds)

		Value = timestamp.ToString()

	Else If $FK(UID_Person).IdentityType$ = "Primary" AndAlso _
		$FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\AmericasMed\AccountDefAmericasMed") AndAlso _
		$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning") AndAlso _
		$UID_TSBBehavior$ = "TSB-FullManaged" AndAlso _
		Not String.IsNullOrEmpty($FK(UID_Person).EntryDate$) Then

		Dim dTime As Date = $FK(UID_Person).EntryDate:Date$

		Dim unixEpoch As Date = New Date(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc)
		Dim timestamp As Long = CLng((dTime.ToUniversalTime() - unixEpoch).TotalSeconds)

		Value = timestamp.ToString()

	End If
End If

' ========================================
' 9. CCC_CustomProperty05 - Template VB
' ========================================
If (Not CBool(Variables("FULLSYNC")) Or _
(CBool(Variables("FULLSYNC")) And ($FK(UID_Person).ImportSource$).StartsWith("SF_HR"))) Then

	If $FK(UID_Person).IdentityType$ = "Primary" AndAlso _
		$FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\UniversidadeDASA\AccountDef") AndAlso _
		$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\UniversidadeDASA") AndAlso _
		$UID_TSBBehavior$ = "TSB-FullManaged" Then

		Value = $FK(UID_Person).CompanyMember$

	Else If $FK(UID_Person).IdentityType$ = "Primary" AndAlso _
		$FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\DASA\AccountDefDasa") AndAlso _
		$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning") AndAlso _
		$UID_TSBBehavior$ = "TSB-FullManaged" Then

		Value = $FK(UID_Person).CompanyMember$

	Else If $FK(UID_Person).IdentityType$ = "Primary" AndAlso _
		$FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\AmericasFunc\AccountDefAmericasFunc") AndAlso _
		$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning") AndAlso _
		$UID_TSBBehavior$ = "TSB-FullManaged" Then

		Value = $FK(UID_Person).CompanyMember$

	Else If $FK(UID_Person).IdentityType$ = "Primary" AndAlso _
		$FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\AmericasMed\AccountDefAmericasMed") AndAlso _
		$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning") AndAlso _
		$UID_TSBBehavior$ = "TSB-FullManaged" Then

		Value = $FK(UID_Person).CompanyMember$

	End If
End If

' ========================================
' 10. CCC_CustomProperty06 - Template VB
' ========================================
If (Not CBool(Variables("FULLSYNC")) Or _
(CBool(Variables("FULLSYNC")) And ($FK(UID_Person).ImportSource$).StartsWith("SF_HR"))) Then

	If $FK(UID_Person).IdentityType$ = "Primary" AndAlso _
		$FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\UniversidadeDASA\AccountDef") AndAlso _
		$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\UniversidadeDASA") AndAlso _
		$UID_TSBBehavior$ = "TSB-FullManaged" Then

		Value = $FK(UID_Person).FK(UID_PersonHead).InternalName$

	Else If $FK(UID_Person).IdentityType$ = "Primary" AndAlso _
		$FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\UniversidadeDASA\AccountDef") AndAlso _
		$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\UniversidadeDASA") AndAlso _
		$UID_TSBBehavior$ = "TSB-FullManaged" AndAlso _
		$FK(UID_Person).IsExternal:bool$ Then

		Value = ""

	Else If $FK(UID_Person).IdentityType$ = "Primary" AndAlso _
		$FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\DASA\AccountDefDasa") AndAlso _
		$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning") AndAlso _
		$UID_TSBBehavior$ = "TSB-FullManaged" Then

		Value = $FK(UID_Person).FK(UID_PersonHead).InternalName$

	Else If $FK(UID_Person).IdentityType$ = "Primary" AndAlso _
		$FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\AmericasFunc\AccountDefAmericasFunc") AndAlso _
		$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning") AndAlso _
		$UID_TSBBehavior$ = "TSB-FullManaged" Then

		Value = $FK(UID_Person).FK(UID_PersonHead).InternalName$

	Else If $FK(UID_Person).IdentityType$ = "Primary" AndAlso _
		$FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\AmericasMed\AccountDefAmericasMed") AndAlso _
		$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning") AndAlso _
		$UID_TSBBehavior$ = "TSB-FullManaged" Then

		Value = $FK(UID_Person).FK(UID_PersonHead).InternalName$

	End If
End If

' ========================================
' 12. CCC_CustomProperty08 - Template VB
' ========================================
If (Not CBool(Variables("FULLSYNC")) Or _
(CBool(Variables("FULLSYNC")) And ($FK(UID_Person).ImportSource$).StartsWith("SF_HR"))) Then

	If $FK(UID_Person).IdentityType$ = "Primary" AndAlso _
		$FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\UniversidadeDASA\AccountDef") AndAlso _
		$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\UniversidadeDASA") AndAlso _
		$UID_TSBBehavior$ = "TSB-FullManaged" Then

		Value = $FK(UID_Person).CustomProperty04$

	Else If $FK(UID_Person).IdentityType$ = "Primary" AndAlso _
		$FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\DASA\AccountDefDasa") AndAlso _
		$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning") AndAlso _
		$UID_TSBBehavior$ = "TSB-FullManaged" Then

		Value = $FK(UID_Person).CustomProperty04$

	Else If $FK(UID_Person).IdentityType$ = "Primary" AndAlso _
		$FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\AmericasFunc\AccountDefAmericasFunc") AndAlso _
		$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning") AndAlso _
		$UID_TSBBehavior$ = "TSB-FullManaged" Then

		Value = $FK(UID_Person).CustomProperty04$

	Else If $FK(UID_Person).IdentityType$ = "Primary" AndAlso _
		$FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\AmericasMed\AccountDefAmericasMed") AndAlso _
		$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning") AndAlso _
		$UID_TSBBehavior$ = "TSB-FullManaged" Then

		Value = $FK(UID_Person).CustomProperty04$
	End If
End If

' ========================================
' 13. CCC_CustomProperty09 - Template VB
' ========================================
If (Not CBool(Variables("FULLSYNC")) Or _
(CBool(Variables("FULLSYNC")) And ($FK(UID_Person).ImportSource$).StartsWith("SF_HR"))) Then

	If $FK(UID_Person).IdentityType$ = "Primary" AndAlso _
		$FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\UniversidadeDASA\AccountDef") AndAlso _
		$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\UniversidadeDASA") AndAlso _
		$UID_TSBBehavior$ = "TSB-FullManaged" Then

		Value = $FK(UID_Person).CustomProperty06$

	Else If $FK(UID_Person).IdentityType$ = "Primary" AndAlso _
		$FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\DASA\AccountDefDasa") AndAlso _
		$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning") AndAlso _
		$UID_TSBBehavior$ = "TSB-FullManaged" Then

		Value = $FK(UID_Person).CustomProperty06$

	Else If $FK(UID_Person).IdentityType$ = "Primary" AndAlso _
		$FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\AmericasFunc\AccountDefAmericasFunc") AndAlso _
		$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning") AndAlso _
		$UID_TSBBehavior$ = "TSB-FullManaged" Then

		Value = $FK(UID_Person).CustomProperty06$

	Else If $FK(UID_Person).IdentityType$ = "Primary" AndAlso _
		$FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\AmericasMed\AccountDefAmericasMed") AndAlso _
		$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning") AndAlso _
		$UID_TSBBehavior$ = "TSB-FullManaged" Then

		Value = $FK(UID_Person).CustomProperty06$
	End If
End If

' ========================================
' 14. CCC_CustomProperty10 - Template VB
' ========================================
If (Not CBool(Variables("FULLSYNC")) Or _
(CBool(Variables("FULLSYNC")) And ($FK(UID_Person).ImportSource$).StartsWith("SF_HR"))) Then

	If $FK(UID_Person).IdentityType$ = "Primary" AndAlso _
		$FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\UniversidadeDASA\AccountDef") AndAlso _
		$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\UniversidadeDASA") AndAlso _
		$UID_TSBBehavior$ = "TSB-FullManaged" Then

		Value = $FK(UID_Person).CustomProperty07$

	Else If $FK(UID_Person).IdentityType$ = "Primary" AndAlso _
		$FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\DASA\AccountDefDasa") AndAlso _
		$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning") AndAlso _
		$UID_TSBBehavior$ = "TSB-FullManaged" Then

		Value = $FK(UID_Person).CustomProperty07$

	Else If $FK(UID_Person).IdentityType$ = "Primary" AndAlso _
		$FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\AmericasFunc\AccountDefAmericasFunc") AndAlso _
		$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning") AndAlso _
		$UID_TSBBehavior$ = "TSB-FullManaged" Then

		Value = $FK(UID_Person).CustomProperty07$

	Else If $FK(UID_Person).IdentityType$ = "Primary" AndAlso _
		$FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\AmericasMed\AccountDefAmericasMed") AndAlso _
		$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning") AndAlso _
		$UID_TSBBehavior$ = "TSB-FullManaged" Then

		Value = $FK(UID_Person).CustomProperty07$
	End If
End If

' ========================================
' 15. CCC_CustomProperty11 - Template VB
' ========================================
If (Not CBool(Variables("FULLSYNC")) Or _
(CBool(Variables("FULLSYNC")) And ($FK(UID_Person).ImportSource$).StartsWith("SF_HR"))) Then
	'Set CPF for CustomProperty11 if TargetSystem is Universidade DASA (UD)
	'Set CPF for CustomProperty11 if Employee or External
	if $FK(UID_Person).IdentityType$ = "Primary" andAlso _
		$FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\UniversidadeDASA\AccountDef") andAlso _
		$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\UniversidadeDASA") andAlso _
		$UID_TSBBehavior$ = "TSB-FullManaged" then

		Value = $FK(UID_Person).CustomProperty02$

	Else if $FK(UID_Person).IdentityType$ = "Primary" andAlso _
		$FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\K2\AccountDef") AndAlso _
		$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\K2\TargetSystemName") andAlso _
		$UID_TSBBehavior$ = "TSB-FullManaged" Then

    	Value = $FK(UID_Person).CustomProperty02$

	Else If $FK(UID_Person).IdentityType$ = "Primary" AndAlso _
		$FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\DASA\AccountDefDasa") AndAlso _
		$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning") AndAlso _
		$UID_TSBBehavior$ = "TSB-FullManaged" Then

		Value = $FK(UID_Person).CustomProperty02$

	Else If $FK(UID_Person).IdentityType$ = "Primary" AndAlso _
		$FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\AmericasFunc\AccountDefAmericasFunc") AndAlso _
		$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning") AndAlso _
		$UID_TSBBehavior$ = "TSB-FullManaged" Then

		Value = $FK(UID_Person).CustomProperty02$

	Else If $FK(UID_Person).IdentityType$ = "Primary" AndAlso _
		$FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\AmericasMed\AccountDefAmericasMed") AndAlso _
		$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning") AndAlso _
		$UID_TSBBehavior$ = "TSB-FullManaged" Then

		Value = $FK(UID_Person).CustomProperty02$

	End if
End If

' ========================================
' 16. CCC_CustomProperty12 - Template VB
' ========================================
If (Not CBool(Variables("FULLSYNC")) Or _
(CBool(Variables("FULLSYNC")) And ($FK(UID_Person).ImportSource$).StartsWith("SF_HR"))) Then

	If Not String.IsNullOrWhiteSpace($UID_Person$) AndAlso $FK(UID_Person).IdentityType$ = "Primary" Then

		If $FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\UniversidadeDASA\AccountDef") AndAlso _
			$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\UniversidadeDASA") AndAlso _
			$UID_TSBBehavior$ = "TSB-FullManaged" Then

			Value = $FK(UID_Person).PersonalTitle$

		Else If $FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\DASA\AccountDefDasa") AndAlso _
			$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning") AndAlso _
			$UID_TSBBehavior$ = "TSB-FullManaged" Then

			Value = $FK(UID_Person).PersonalTitle$

		Else If $FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\AmericasFunc\AccountDefAmericasFunc") AndAlso _
			$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning") AndAlso _
			$UID_TSBBehavior$ = "TSB-FullManaged" Then

			Value = $FK(UID_Person).PersonalTitle$

		Else If $FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\AmericasMed\AccountDefAmericasMed") AndAlso _
			$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning") AndAlso _
			$UID_TSBBehavior$ = "TSB-FullManaged" Then

			Value = $FK(UID_Person).PersonalTitle$

		End If
	End If
End If

' ========================================
' 17. CCC_CustomProperty13 - Template VB
' ========================================
If (Not CBool(Variables("FULLSYNC")) Or _
(CBool(Variables("FULLSYNC")) And ($FK(UID_Person).ImportSource$).StartsWith("SF_HR"))) Then

	If Not String.IsNullOrWhiteSpace($UID_Person$) AndAlso $FK(UID_Person).IdentityType$ = "Primary" Then

		'Set Gender for CustomProperty13 if TargetSystem is Universidade DASA (UD)
		If $FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\UniversidadeDASA\AccountDef") AndAlso _
			$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\UniversidadeDASA") AndAlso _
			$UID_TSBBehavior$ = "TSB-FullManaged" Then

			' Set Gender for CustomProperty13 if Employee
			If $FK(UID_Person).Gender$ = "1" Then
					Value = "Masculino"
				Else If $FK(UID_Person).Gender$ = "2" Then
					Value = "Feminino"
				Else
					Value = "Desconhecido"
			End If

		Else If $FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\DASA\AccountDefDasa") AndAlso _
			$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning") AndAlso _
			$UID_TSBBehavior$ = "TSB-FullManaged" Then

				If $FK(UID_Person).Gender$ = "1" Then
					Value = "Masculino"
				Else If $FK(UID_Person).Gender$ = "2" Then
					Value = "Feminino"
				Else
					Value = "Desconhecido"

			End If

		Else If $FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\AmericasFunc\AccountDefAmericasFunc") AndAlso _
			$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning") AndAlso _
			$UID_TSBBehavior$ = "TSB-FullManaged" Then

				If $FK(UID_Person).Gender$ = "1" Then
					Value = "Masculino"
				Else If $FK(UID_Person).Gender$ = "2" Then
					Value = "Feminino"
				Else
					Value = "Desconhecido"

			End If

		Else If $FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\AmericasMed\AccountDefAmericasMed") AndAlso _
			$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning") AndAlso _
			$UID_TSBBehavior$ = "TSB-FullManaged" Then

				If $FK(UID_Person).Gender$ = "1" Then
					Value = "Masculino"
				Else If $FK(UID_Person).Gender$ = "2" Then
					Value = "Feminino"
				Else
					Value = "Desconhecido"

			End If
		End If
	End If
End If

' ========================================
' 18. CCC_CustomProperty14 - Template VB
' ========================================
If (Not CBool(Variables("FULLSYNC")) Or _
(CBool(Variables("FULLSYNC")) And ($FK(UID_Person).ImportSource$).StartsWith("SF_HR"))) _
AndAlso Not String.IsNullOrWhiteSpace($UID_Person$) AndAlso $FK(UID_Person).IdentityType$ = "Primary" Then
	
	'Set BirthDate for CustomProperty14 if TargetSystem is Universidade DASA (UD)
	If $FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\UniversidadeDASA\AccountDef") AndAlso _
		$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\UniversidadeDASA") AndAlso _
		$UID_TSBBehavior$ = "TSB-FullManaged" Then
		
		'Set BirthDate for CustomProperty14 if Employee
		If Not String.IsNullOrEmpty($FK(UID_Person).BirthDate$)	Then
			
			Dim dTime As Date = $FK(UID_Person).BirthDate:Date$

			Value = dTime.ToString("dd/MM/yyyy")
		
		End If
		
	Else If $FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\DASA\AccountDefDasa") AndAlso _
		$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning") AndAlso _
		$UID_TSBBehavior$ = "TSB-FullManaged" Then

		If Not String.IsNullOrEmpty($FK(UID_Person).BirthDate$)	Then

			Dim dTime As Date = $FK(UID_Person).BirthDate:Date$

			Dim unixEpoch As Date = New Date(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc)
			Dim timestamp As Long = CLng((dTime.ToUniversalTime() - unixEpoch).TotalSeconds)

			Value = timestamp.ToString()

		End If

	Else If $FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\AmericasFunc\AccountDefAmericasFunc") AndAlso _
		$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning") AndAlso _
		$UID_TSBBehavior$ = "TSB-FullManaged" Then

		If Not String.IsNullOrEmpty($FK(UID_Person).BirthDate$)	Then

			Dim dTime As Date = $FK(UID_Person).BirthDate:Date$

			Dim unixEpoch As Date = New Date(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc)
			Dim timestamp As Long = CLng((dTime.ToUniversalTime() - unixEpoch).TotalSeconds)

			Value = timestamp.ToString()

		End If

	Else If $FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\AmericasMed\AccountDefAmericasMed") AndAlso _
		$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning") AndAlso _
		$UID_TSBBehavior$ = "TSB-FullManaged" Then

		If Not String.IsNullOrEmpty($FK(UID_Person).BirthDate$)	Then

			Dim dTime As Date = $FK(UID_Person).BirthDate:Date$

			Dim unixEpoch As Date = New Date(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc)
			Dim timestamp As Long = CLng((dTime.ToUniversalTime() - unixEpoch).TotalSeconds)

			Value = timestamp.ToString()

		End If
	End If
End If

' ========================================
' 19. CCC_CustomProperty15 - Template VB
' ========================================
If (Not CBool(Variables("FULLSYNC")) Or _
(CBool(Variables("FULLSYNC")) And ($FK(UID_Person).ImportSource$).StartsWith("SF_HR"))) Then
	'Set DefaultEmailAddress for CustomProperty15 if TargetSystem is Universidade DASA (UD)
	'Set DefaultEmailAddress for CustomProperty15 if Employee
	If $FK(UID_Person).IdentityType$ = "Primary" AndAlso _
		$FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\UniversidadeDASA\AccountDef") AndAlso _
		$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\UniversidadeDASA") AndAlso _
		$UID_TSBBehavior$ = "TSB-FullManaged" Then
		
		Value = $FK(UID_Person).DefaultEmailAddress$
	
	'Set Empty Value for CustomProperty15 if External
			
	Else If $FK(UID_Person).IdentityType$ = "Primary" AndAlso _
		$FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\K2\AccountDef") AndAlso _
		$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\K2\TargetSystemName") AndAlso _
		$UID_TSBBehavior$ = "TSB-FullManaged" Then
		
		Value = $FK(UID_Person).DefaultEmailAddress$
		
	Else If $FK(UID_Person).IdentityType$ = "Primary" AndAlso _
		$FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\DASA\AccountDefDasa") AndAlso _
		$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning") AndAlso _
		$UID_TSBBehavior$ = "TSB-FullManaged" Then

		Value = $FK(UID_Person).DefaultEmailAddress$

	Else If $FK(UID_Person).IdentityType$ = "Primary" AndAlso _
		$FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\AmericasFunc\AccountDefAmericasFunc") AndAlso _
		$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning") AndAlso _
		$UID_TSBBehavior$ = "TSB-FullManaged" Then

		Value = $FK(UID_Person).DefaultEmailAddress$

	Else If $FK(UID_Person).IdentityType$ = "Primary" AndAlso _
		$FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\AmericasMed\AccountDefAmericasMed") AndAlso _
		$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning") AndAlso _
		$UID_TSBBehavior$ = "TSB-FullManaged" Then

		Value = $FK(UID_Person).DefaultEmailAddress$

	End If
End If

' ========================================
' 19. CCC_CustomProperty19 - Template VB
' ========================================
If $FK(UID_Person).IdentityType$ = "Primary" AndAlso _
	$FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\DASA\AccountDefDasa") AndAlso _
	$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning") AndAlso _
	$UID_TSBBehavior$ = "TSB-FullManaged" Then
	
	Value = $FK(UID_Person).FK(UID_FirmPartner).Name1:Text$

Else If $FK(UID_Person).IdentityType$ = "Primary" AndAlso _
	$FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\AmericasFunc\AccountDefAmericasFunc") AndAlso _
	$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning") AndAlso _
	$UID_TSBBehavior$ = "TSB-FullManaged" Then

	Value = $FK(UID_Person).FK(UID_FirmPartner).Name1:Text$

Else If $FK(UID_Person).IdentityType$ = "Primary" AndAlso _
	$FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\AmericasMed\AccountDefAmericasMed") AndAlso _
	$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning") AndAlso _
	$UID_TSBBehavior$ = "TSB-FullManaged" Then

	Value = $FK(UID_Person).FK(UID_FirmPartner).Name1:Text$
	
End If

' ========================================
' 19. CCC_CustomProperty21 - Template VB
' ========================================
If $FK(UID_Person).IdentityType$ = "Primary" AndAlso _
	$FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\DASA\AccountDefDasa") AndAlso _
	$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning") AndAlso _
	$UID_TSBBehavior$ = "TSB-FullManaged" Then

	Value = $FK(UID_Person).FK(UID_ProfitCenter).ShortName$

Else If $FK(UID_Person).IdentityType$ = "Primary" AndAlso _
	$FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\AmericasFunc\AccountDefAmericasFunc") AndAlso _
	$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning") AndAlso _
	$UID_TSBBehavior$ = "TSB-FullManaged" Then

	Value = $FK(UID_Person).FK(UID_ProfitCenter).ShortName$

Else If $FK(UID_Person).IdentityType$ = "Primary" AndAlso _
	$FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\AmericasMed\AccountDefAmericasMed") AndAlso _
	$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning") AndAlso _
	$UID_TSBBehavior$ = "TSB-FullManaged" Then

	Value = $FK(UID_Person).FK(UID_ProfitCenter).ShortName$

End If

' ========================================
' 19. CCC_CustomProperty23 - Template VB
' ========================================
If $FK(UID_Person).IdentityType$ = "Primary" AndAlso _
	$FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\DASA\AccountDefDasa") AndAlso _
	$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning") AndAlso _
	$UID_TSBBehavior$ = "TSB-FullManaged" Then

	Value = $FK(UID_Person).FK(UID_Locality).FK(UID_DialogState).Ident_DialogState$

Else If $FK(UID_Person).IdentityType$ = "Primary" AndAlso _
	$FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\AmericasFunc\AccountDefAmericasFunc") AndAlso _
	$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning") AndAlso _
	$UID_TSBBehavior$ = "TSB-FullManaged" Then

	Value = $FK(UID_Person).FK(UID_Locality).FK(UID_DialogState).Ident_DialogState$

Else If $FK(UID_Person).IdentityType$ = "Primary" AndAlso _
	$FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\AmericasMed\AccountDefAmericasMed") AndAlso _
	$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning") AndAlso _
	$UID_TSBBehavior$ = "TSB-FullManaged" Then

	Value = $FK(UID_Person).FK(UID_Locality).FK(UID_DialogState).Ident_DialogState$

End If

' ========================================
' 19. CCC_CustomProperty25 - Template VB
' ========================================
If $FK(UID_Person).IdentityType$ = "Primary" AndAlso _
	$FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\DASA\AccountDefDasa") AndAlso _
	$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning") AndAlso _
	$UID_TSBBehavior$ = "TSB-FullManaged" Then

	Value = $FK(UID_Person).PhoneMobile:Text$

Else If $FK(UID_Person).IdentityType$ = "Primary" AndAlso _
	$FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\AmericasFunc\AccountDefAmericasFunc") AndAlso _
	$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning") AndAlso _
	$UID_TSBBehavior$ = "TSB-FullManaged" Then

	Value = $FK(UID_Person).PhoneMobile:Text$

Else If $FK(UID_Person).IdentityType$ = "Primary" AndAlso _
	$FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\AmericasMed\AccountDefAmericasMed") AndAlso _
	$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning") AndAlso _
	$UID_TSBBehavior$ = "TSB-FullManaged" Then

	Value = $FK(UID_Person).PhoneMobile:Text$

End If

' ========================================
' 19. CCC_CustomProperty26 - Template VB
' ========================================
If $FK(UID_Person).IdentityType$ = "Primary" AndAlso _
	$FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\DASA\AccountDefDasa") AndAlso _
	$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning") AndAlso _
	$UID_TSBBehavior$ = "TSB-FullManaged" Then

	Value = $FK(UID_Person).CCC_CRM$

Else If $FK(UID_Person).IdentityType$ = "Primary" AndAlso _
	$FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\AmericasFunc\AccountDefAmericasFunc") AndAlso _
	$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning") AndAlso _
	$UID_TSBBehavior$ = "TSB-FullManaged" Then

	Value = $FK(UID_Person).CCC_CRM$

Else If $FK(UID_Person).IdentityType$ = "Primary" AndAlso _
	$FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\AmericasMed\AccountDefAmericasMed") AndAlso _
	$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning") AndAlso _
	$UID_TSBBehavior$ = "TSB-FullManaged" Then

	Value = $FK(UID_Person).CCC_CRM$

End If

' ========================================
' 19. CCC_CustomProperty27 - Template VB
' ========================================
If $FK(UID_Person).IdentityType$ = "Primary" AndAlso _
	$FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\DASA\AccountDefDasa") AndAlso _
	$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning") AndAlso _
	$UID_TSBBehavior$ = "TSB-FullManaged" Then

	Value = $FK(UID_Person).CCC_EspecialidadesMedicas$

Else If $FK(UID_Person).IdentityType$ = "Primary" AndAlso _
	$FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\AmericasFunc\AccountDefAmericasFunc") AndAlso _
	$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning") AndAlso _
	$UID_TSBBehavior$ = "TSB-FullManaged" Then

	Value = $FK(UID_Person).CCC_EspecialidadesMedicas$

Else If $FK(UID_Person).IdentityType$ = "Primary" AndAlso _
	$FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\AmericasMed\AccountDefAmericasMed") AndAlso _
	$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning") AndAlso _
	$UID_TSBBehavior$ = "TSB-FullManaged" Then

	Value = $FK(UID_Person).CCC_EspecialidadesMedicas$

End If

' ========================================
' 19. CCC_CustomProperty30 - Template VB
' ========================================
If (Not CBool(Variables("FULLSYNC")) Or _
(CBool(Variables("FULLSYNC")) And ($FK(UID_Person).ImportSource$).StartsWith("SF_HR"))) Then

	If $FK(UID_Person).IdentityType$ = "Primary" AndAlso _
		$FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\UniversidadeDASA\AccountDef") AndAlso _
		$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\UniversidadeDASA") AndAlso _
		$UID_TSBBehavior$ = "TSB-FullManaged" Then
		
		Value = $FK(UID_Person).CustomProperty05$
	
	Else If $FK(UID_Person).IdentityType$ = "Primary" AndAlso _
		$FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\UniversidadeDASA\AccountDef") AndAlso _
		$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\UniversidadeDASA") AndAlso _
		$UID_TSBBehavior$ = "TSB-FullManaged" AndAlso _
		$FK(UID_Person).IsExternal:bool$ Then

		Value = ""
		
	Else If $FK(UID_Person).IdentityType$ = "Primary" AndAlso _
		$FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\DASA\AccountDefDasa") AndAlso _
		$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning") AndAlso _
		$UID_TSBBehavior$ = "TSB-FullManaged" Then

		Value = $FK(UID_Person).CustomProperty05$

	Else If $FK(UID_Person).IdentityType$ = "Primary" AndAlso _
		$FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\AmericasFunc\AccountDefAmericasFunc") AndAlso _
		$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning") AndAlso _
		$UID_TSBBehavior$ = "TSB-FullManaged" Then

		Value = $FK(UID_Person).CustomProperty05$

	Else If $FK(UID_Person).IdentityType$ = "Primary" AndAlso _
		$FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\AmericasMed\AccountDefAmericasMed") AndAlso _
		$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning") AndAlso _
		$UID_TSBBehavior$ = "TSB-FullManaged" Then

		Value = $FK(UID_Person).CustomProperty05$

	End If
End If