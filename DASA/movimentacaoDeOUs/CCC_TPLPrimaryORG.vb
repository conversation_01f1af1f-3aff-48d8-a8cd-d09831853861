Public Function CCC_TPL_PrimaryORG1(strCompanyMember As String, strDialogState As String, strIsInactive As Boolean, 
                                    strCustomProperty04 As String, strCustomProperty06 As String, strCustomProperty09 As String, 
                                    IsTemporaryDeactivated <PERSON> Boolean, IsExternal As Boolean, strCity As String, 
                                    crm As String, strIdentityType As String, strPersonMasterIdentity As String, 
                                    strCentralAccount As String, ByVal br As String, ByVal entryDate As Date) As String

    ' Constantes para melhorar legibilidade e manutenção
    Const AD_MAPPING_PREFIX As String = "AD-Mapping: "
    Const CONFIG_PATH_PREFIX As String = "Custom\AD\Mapping\"
    
    ' Inicialização de variáveis
    Dim StrResult As String = ""
    Dim StrComparison As String = ""
    Dim StrConfigParam As String = ""
    Dim firstLetter As String = Left(strCentralAccount, 1)
    Dim StrTmp As String = strCompanyMember.Replace(" ","")
    Dim f As ISqlFormatter = Connection.SqlFormatter
    
    ' Verificação prioritária de ajuste de usuários específicos
    Dim ajusteResult As String = CCC_AjustaUsers(strCentralAccount)
    If Not String.IsNullOrWhiteSpace(ajusteResult) Then
        Return ajusteResult
    End If
    
    ' Obter caminho completo da organização
    Dim fullPath As String = Connection.GetSingleProperty("Org", "FullPath", _
                f.uidcomparison("UID_Org", br))
    
    ' Verificação de OUs exclusivas
    If IsPathInExclusiveList(fullPath) Then
        Return br
    End If
    
    ' Verificação de casos especiais por empresa e data de entrada
    If IsCompanyMemberWithEntryDateRule(strCompanyMember, entryDate) Then
        Return GetOrgUidByPath(AD_MAPPING_PREFIX & strCompanyMember)
    End If
    
    ' Processamento principal
    Try
        ' Verificação de contas desabilitadas e sub-identidades
        If ProcessAccountStatusRules(strIsInactive, strIdentityType, firstLetter, strPersonMasterIdentity, StrResult) Then
            Return StrResult
        End If
        
        ' Verificação de contas externas
        If ProcessExternalAccounts(IsExternal, crm, StrResult) Then
            Return StrResult
        End If
        
        ' Verificação de funcionários afastados
        If ProcessTemporaryDeactivated(IsTemporaryDeactivated, strCustomProperty09, StrResult) Then
            Return StrResult
        End If
        
        ' Verificação de executivos
        If ProcessExecutiveGroup(strCustomProperty04, strDialogState, StrResult) Then
            Return StrResult
        End If
        
        ' Verificação de cidade Cascavél
        If ProcessCascavelCity(strCity, strCustomProperty06, strCompanyMember, StrResult) Then
            Return StrResult
        End If
        
        ' Verificação de funcionários de TI
        If ProcessITDepartment(strCustomProperty06, strDialogState, StrResult) Then
            Return StrResult
        End If
        
        ' Verificação de operadores NAC
        If ProcessNACOperators(strCustomProperty06, strDialogState, StrResult) Then
            Return StrResult
        End If
        
        ' Processamento padrão baseado na empresa
        ProcessDefaultCompany(StrTmp, strDialogState, strCompanyMember, StrResult)
        
    Catch ex As Exception
        Throw New Exception("Error in script CCC_TPL_PrimaryORG1: ", ex)
    Finally
        CCC_TPL_PrimaryORG1 = StrResult
    End Try

    Return StrResult
End Function

' Função para verificar se o caminho está na lista de exclusivos
Private Function IsPathInExclusiveList(fullPath As String) As Boolean
    Dim brExclusive As String() = {
        "AD-Mapping: CHN", "AD-Mapping: SANTA CELINA GI", "AD-Mapping: SAÚDE CELINA",
        "AD-Mapping: IMPAR", "AD-Mapping: INNOVA", "AD-Mapping: H9J",
        "AD-Mapping: HOSPITAL SÃO LUCAS", "AD-Mapping: HOSPITAL BRASÍLIA",
        "AD-Mapping: HOSPITAL SANTA PAULA", "AD-Mapping: CHN", "AD-Mapping: SP-EXECUTIVOS"
    }
    
    Return brExclusive.Contains(fullPath)
End Function

' Função para verificar regra de empresa e data de entrada
Private Function IsCompanyMemberWithEntryDateRule(companyMember As String, entryDate As Date) As Boolean
    Dim compMemberList As String() = { 
        "HOSPITAL SÃO LUCAS", "HOSPITAL BRASÍLIA", "HOSPITAL ÁGUAS CLARA", 
        "HOSPITAL SANTA PAULA", "H9J", "IMPAR", "CHN", 
        "SANTA CELINA GI", "SAÚDE CELINA", "INNOVA" 
    }
    
    Dim pointEntryDate As Date = Convert.ToDateTime("4/2/2022")
    
    Return compMemberList.Contains(companyMember) And entryDate >= pointEntryDate
End Function

' Função para processar regras de status de conta
Private Function ProcessAccountStatusRules(isInactive As Boolean, identityType As String, firstLetter As String, personMasterIdentity As String, ByRef result As String) As Boolean
    Const AD_MAPPING_PREFIX As String = "AD-Mapping: "
    Const CONFIG_PATH_PREFIX As String = "Custom\AD\Mapping\"
    
    If isInactive And identityType = "Primary" Then
        result = GetOrgUidByPath(AD_MAPPING_PREFIX & "DESABILITADOS")
        Return True
    End If
    
    ' Verificação de sub-identidades por tipo de letra inicial
    If identityType = "Admin" And personMasterIdentity.Length > 0 Then
        Dim configType As String = If(isInactive, "DESABILITADOS-CONTA-", "SUB-IDENTITY-CONTA-")
        
        If firstLetter = "A" Or firstLetter = "O" Or firstLetter = "S" Then
            Dim configParam As String = CONFIG_PATH_PREFIX & configType & firstLetter
            result = GetOrgUidByPath(AD_MAPPING_PREFIX & Connection.GetConfigParm(configParam))
            Return True
        End If
    End If
    
    Return False
End Function

' Função para processar contas externas
Private Function ProcessExternalAccounts(isExternal As Boolean, crm As String, ByRef result As String) As Boolean
    Const AD_MAPPING_PREFIX As String = "AD-Mapping: "
    
    If isExternal Then
        If crm.Length = 0 Then
            result = GetOrgUidByPath(AD_MAPPING_PREFIX & "TERCEIROS")
        Else
            result = GetOrgUidByPath(AD_MAPPING_PREFIX & "MEDICOS")
        End If
        Return True
    End If
    
    Return False
End Function

' Função para processar funcionários temporariamente desativados
Private Function ProcessTemporaryDeactivated(isTemporaryDeactivated As Boolean, customProperty09 As String, ByRef result As String) As Boolean
    Const AD_MAPPING_PREFIX As String = "AD-Mapping: "
    
    If isTemporaryDeactivated AndAlso customProperty09.Length > 0 AndAlso _
       customProperty09.Split(" "c)(0) <> Connection.GetConfigParm("Custom\AD\Mapping\Ferias") Then
        result = GetOrgUidByPath(AD_MAPPING_PREFIX & "AFASTADOS")
        Return True
    End If
    
    Return False
End Function

' Função para processar grupo de executivos
Private Function ProcessExecutiveGroup(customProperty04 As String, dialogState As String, ByRef result As String) As Boolean
    Const AD_MAPPING_PREFIX As String = "AD-Mapping: "
    
    If customProperty04.ToUpper = "EXECUTIVOS" Then
        If dialogState.ToUpper <> "SP" AndAlso dialogState.ToUpper <> "RJ" Then
            result = GetOrgUidByPath(AD_MAPPING_PREFIX & "SP-EXECUTIVOS")
        Else 
            result = GetOrgUidByPath(AD_MAPPING_PREFIX & dialogState.ToUpper & "-" & customProperty04.ToUpper)
        End If
        Return True
    End If
    
    Return False
End Function

' Função para processar cidade Cascavél
Private Function ProcessCascavelCity(city As String, customProperty06 As String, companyMember As String, ByRef result As String) As Boolean
    Const AD_MAPPING_PREFIX As String = "AD-Mapping: "
    Const CONFIG_PATH_PREFIX As String = "Custom\AD\Mapping\"
    
    If city.ToUpper = "CASCAVÉL" Then
        If customProperty06.ToUpper = "TECNOL DA INFORMAÇÃO" Then
            result = GetOrgUidByPath(AD_MAPPING_PREFIX & city.ToUpper & "-" & customProperty06.ToUpper)
            
            If result.Length = 0 Then
                result = GetOrgUidByPath(AD_MAPPING_PREFIX & "SP-DASA-TECNOL DA INFORMAÇÃO")
            End If
        Else
            result = GetOrgUidByPath(AD_MAPPING_PREFIX & city.ToUpper & "-" & companyMember.ToUpper)
            
            If result.Length = 0 Then
                Dim configParam As String = CONFIG_PATH_PREFIX & city.ToUpper
                result = GetOrgUidByPath(AD_MAPPING_PREFIX & Connection.GetConfigParm(configParam))
            End If
        End If
        Return True
    End If
    
    Return False
End Function

' Função para processar departamento de TI
Private Function ProcessITDepartment(customProperty06 As String, dialogState As String, ByRef result As String) As Boolean
    Const AD_MAPPING_PREFIX As String = "AD-Mapping: "
    
    If customProperty06.ToUpper = "TECNOL DA INFORMAÇÃO" Then
        result = GetOrgUidByPath(AD_MAPPING_PREFIX & dialogState.ToUpper & "-DASA-" & customProperty06.ToUpper)
        
        If result.Length = 0 Then
            result = GetOrgUidByPath(AD_MAPPING_PREFIX & "SP-DASA-TECNOL DA INFORMAÇÃO")
        End If
        Return True
    End If
    
    Return False
End Function

' Função para processar operadores NAC
Private Function ProcessNACOperators(customProperty06 As String, dialogState As String, ByRef result As String) As Boolean
    Const AD_MAPPING_PREFIX As String = "AD-Mapping: "
    
    If customProperty06.ToUpper = "NAC" Then
        result = GetOrgUidByPath(AD_MAPPING_PREFIX & dialogState.ToUpper & "-" & customProperty06.ToUpper)
        
        If result.Length = 0 Then
            result = GetOrgUidByPath(AD_MAPPING_PREFIX & "SP-NAC")
        End If
        Return True
    End If
    
    Return False
End Function

' Função para processar empresa padrão
Private Function ProcessDefaultCompany(companyTmp As String, dialogState As String, companyMember As String, ByRef result As String) As Boolean
    Const AD_MAPPING_PREFIX As String = "AD-Mapping: "
    Const CONFIG_PATH_PREFIX As String = "Custom\AD\Mapping\"
    
    Select Case companyTmp.ToUpper 
        Case "DASA" 
            result = GetOrgUidByPath(AD_MAPPING_PREFIX & dialogState.ToUpper & "-" & companyMember.ToUpper & "-ADMINISTRATIVO")
        
            If result.Length = 0 Then
                Dim configParam As String = CONFIG_PATH_PREFIX & companyTmp.ToUpper
                result = GetOrgUidByPath(AD_MAPPING_PREFIX & Connection.GetConfigParm(configParam))
            End If
        
        Case Else
            result = GetOrgUidByPath(AD_MAPPING_PREFIX & dialogState.ToUpper & "-" & companyMember.ToUpper)
        
            If result.Length = 0 Then
                Dim configParam As String = CONFIG_PATH_PREFIX & companyTmp.ToUpper
                result = GetOrgUidByPath(AD_MAPPING_PREFIX & Connection.GetConfigParm(configParam))
                
                If result.Length = 0 Then
                    Dim configParam2 As String = CONFIG_PATH_PREFIX & "STANDARD"
                    result = GetOrgUidByPath(AD_MAPPING_PREFIX & Connection.GetConfigParm(configParam2))
                End If
            End If
    End Select
    
    Return True
End Function

' Função auxiliar para obter UID_Org a partir do caminho
Private Function GetOrgUidByPath(path As String) As String
    Dim f As ISqlFormatter = Connection.SqlFormatter
    Return Connection.GetSingleProperty("Org", "UID_Org", _
        f.comparison("FullPath", path, ValType.String, CompareOperator.Equal, FormatterOptions.IgnoreCase))
End Function


