Public Function CCC_AjustaUsers(ByVal strCentralAccount As String) As String

    ' Define o formatador SQL uma vez
    Const AD_MAPPING_PREFIX As String = "AD-Mapping: "
    Dim sqlFormatter As ISqlFormatter = Connection.SqlFormatter

    ' Lista de usuários que devem ser direcionados para a OU SP-EXECUTIVOS
    ' IMPORTANTE: Mantenha esta lista atualizada. Se ficar muito grande,
    ' considere mover para um Parâmetro de Configuração ou tabela auxiliar no OIM.
    Dim usuariosNewOu As String() = {
        "F22653388855",
        "F51145909876",
        "F04716017648",
        "F38816291818",
        "F46362257839",
        "F49774023838",
        "F42584964805",
        "F14314081660",
        "F48717958881",
        "F41557568898",
        "F46248297860",
        "F46314256879",
        "F40459006835",
        "F34859719875",
        "F47986313889",
        "F27065813878",
        "F47311289866",
        "F47515654864",
        "F47750368825",
        "F08686541402",
        "F50917911830",
        "F43502668876",
        "F21304124835",
        "F35288097879",
        "F45101581810",
        "F39258975855",
        "F06427008188",
        "F15362084852",
        "F33852230837",
        "F36142454899",
        "F43502195870",
        "F02817642392",
        "F29672003870",
        "F13456550880",
        "F20632399830",
        "F34836491827",
        "F25320369824",
        "F02921968657",
        "F28287918820",
        "F00307098303",
        "F30517786800",
        "F33329352833",
        "F32217066854",
        "F22056723808",
        "F33740502800",
        "F02204824380",
        "F03145083686",
        "F97723126700",
        "F81982720620",
        "F15099551866",
        "F86108409991",
        "F09486484813",
        "F40495272949",
        "F17610943860",
        "F06328347812",
        "F33773211791",
        "F02338018710",
        "F45670161149",
        "F02214177701",
        "F72987790104",
        "F01703616596",
        "F29435515894",
        "F52905454334",
        "F28364739832",
        "S28364739832",
        "S84130083520",
        "A22964862826",
        "A70229213189"
    }

    If usuariosNewOu.Contains(strCentralAccount) Then
        ' Se o usuário estiver na lista, retorna o UID_Org de SP-EXECUTIVOS
        Return Connection.GetSingleProperty("Org", "UID_Org", _
            sqlFormatter.comparison("FullPath", AD_MAPPING_PREFIX & "SP-EXECUTIVOS", ValType.String, CompareOperator.Equal, FormatterOptions.IgnoreCase))
    Else
        ' Caso contrário, retorna uma string vazia, indicando que este script não aplicou um ajuste
        Return ""
    End If

End Function