Public Function CCC_TPL_PrimaryORG1(strCompanyMember As String, strDialogState As String, strIsInactive As Boolean, 
                                    strCustomProperty04 As String, strCustomProperty06 As String, strCustomProperty09 As String, 
                                    IsTemporaryDeactivated As Boolean, IsExternal As Boolean, strCity As String, 
                                    crm As String, strIdentityType As String, strPersonMasterIdentity As String, 
                                    strCentralAccount As String, ByVal br As String, ByVal entryDate As Date) As String

    ' Constantes para melhorar legibilidade
    Const AD_MAPPING_PREFIX As String = "AD-Mapping: "
    
    ' Inicialização de variáveis
    Dim StrResult As String = ""
    Dim StrComparison As String = ""
    Dim f As ISqlFormatter = Connection.SqlFormatter
    Dim g As ISqlFormatter = Connection.SqlFormatter
    
    ' Verificação prioritária de ajuste de usuários específicos
    Dim ajusteResult As String = CCC_AjustaUsers(strCentralAccount)
    If Not String.IsNullOrWhiteSpace(ajusteResult) Then
        Return ajusteResult
    End If
    
    ' Listas e configurações
    Dim pointEntryDate As Date = Convert.ToDateTime("4/2/2022")
    Dim compMemberList As String() = { 
        "HOSPITAL SÃO LUCAS", "HOSPITAL BRASÍLIA", "HOSPITAL ÁGUAS CLARA", 
        "HOSPITAL SANTA PAULA", "H9J", "IMPAR", "CHN", 
        "SANTA CELINA GI", "SAÚDE CELINA", "INNOVA" 
    }
    
    ' Obter caminho completo da organização
    Dim fullPath As String = Connection.GetSingleProperty("Org", "FullPath", _
                f.uidcomparison("UID_Org", br))
    
    ' Verificação de casos especiais
    If compMemberList.Contains(strCompanyMember) And entryDate >= pointEntryDate Then
        StrResult = GetOrgUidByPath(AD_MAPPING_PREFIX & strCompanyMember)
        Return StrResult
    ElseIf brExclusive.Contains(fullPath) Then
        Return br
    End If
    
    Try
        ' Processamento principal baseado em condições
        If ProcessDisabledAccounts(strIsInactive, strIdentityType, strCentralAccount, strPersonMasterIdentity, StrResult) Then
            ' Resultado já definido na função
        ElseIf ProcessExternalAccounts(IsExternal, crm, StrResult) Then
            ' Resultado já definido na função
        ElseIf ProcessTemporaryDeactivated(IsTemporaryDeactivated, strCustomProperty09, StrResult) Then
            ' Resultado já definido na função
        ElseIf ProcessExecutiveGroup(strCustomProperty04, strDialogState, StrResult) Then
            ' Resultado já definido na função
        ElseIf ProcessCascavelCity(strCity, strCustomProperty06, strCompanyMember, StrResult) Then
            ' Resultado já definido na função
        ElseIf ProcessITDepartment(strCustomProperty06, strDialogState, StrResult) Then
            ' Resultado já definido na função
        ElseIf ProcessNACOperators(strCustomProperty06, strDialogState, StrResult) Then
            ' Resultado já definido na função
        Else
            ' Processamento padrão baseado na empresa
            ProcessDefaultCompany(strCompanyMember, strDialogState, StrResult)
        End If
    
    Catch ex As Exception
        Throw New Exception("Error in script CCC_TPL_PrimaryORG1: ", ex)
    Finally
        CCC_TPL_PrimaryORG1 = StrResult
    End Try

End Function

' Funções auxiliares para melhorar a organização e legibilidade

Private Function GetOrgUidByPath(path As String) As String
    Dim f As ISqlFormatter = Connection.SqlFormatter
    Return Connection.GetSingleProperty("Org", "UID_Org", _
        f.comparison("FullPath", path, ValType.String, CompareOperator.Equal, FormatterOptions.IgnoreCase))
End Function

Private Function ProcessDisabledAccounts(strIsInactive As Boolean, strIdentityType As String, 
                                        strCentralAccount As String, strPersonMasterIdentity As String, 
                                        ByRef result As String) As Boolean
    Dim firstLetter As String = Left(strCentralAccount, 1)
    Dim f As ISqlFormatter = Connection.SqlFormatter
    
    If strIsInactive And strIdentityType = "Primary" Then
        result = GetOrgUidByPath("AD-Mapping: DESABILITADOS")
        Return True
    ElseIf strIsInactive And strIdentityType = "Admin" And firstLetter = "A" And strPersonMasterIdentity.Length > 0 Then
        Dim configParam = "Custom\AD\Mapping\DESABILITADOS-CONTA-A"
        result = GetOrgUidByPath("AD-Mapping: " & Connection.GetConfigParm(configParam))
        Return True
    ElseIf Not strIsInactive And strIdentityType = "Admin" And firstLetter = "A" And strPersonMasterIdentity.Length > 0 Then
        Dim configParam = "Custom\AD\Mapping\SUB-IDENTITY-CONTA-A"
        result = GetOrgUidByPath("AD-Mapping: " & Connection.GetConfigParm(configParam))
        Return True
    ElseIf strIsInactive And strIdentityType = "Admin" And firstLetter = "O" And strPersonMasterIdentity.Length > 0 Then
        Dim configParam = "Custom\AD\Mapping\DESABILITADOS-CONTA-O"
        result = GetOrgUidByPath("AD-Mapping: " & Connection.GetConfigParm(configParam))
        Return True
    ElseIf Not strIsInactive And strIdentityType = "Admin" And firstLetter = "O" And strPersonMasterIdentity.Length > 0 Then
        Dim configParam = "Custom\AD\Mapping\SUB-IDENTITY-CONTA-O"
        result = GetOrgUidByPath("AD-Mapping: " & Connection.GetConfigParm(configParam))
        Return True
    ElseIf strIsInactive And strIdentityType = "Admin" And firstLetter = "S" And strPersonMasterIdentity.Length > 0 Then
        Dim configParam = "Custom\AD\Mapping\DESABILITADOS-CONTA-S"
        result = GetOrgUidByPath("AD-Mapping: " & Connection.GetConfigParm(configParam))
        Return True
    ElseIf Not strIsInactive And strIdentityType = "Admin" And firstLetter = "S" And strPersonMasterIdentity.Length > 0 Then
        Dim configParam = "Custom\AD\Mapping\SUB-IDENTITY-CONTA-S"
        result = GetOrgUidByPath("AD-Mapping: " & Connection.GetConfigParm(configParam))
        Return True
    End If
    
    Return False
End Function

' Outras funções auxiliares seguiriam o mesmo padrão...