Public Function CCC_TPL_PrimaryORG1(ByVal strCompanyMember As String, ByVal strDialogState As String, ByVal strIsInactive As Boolean, ByVal strCustomProperty04 As String, ByVal strCustomProperty06 As String, ByVal strCustomProperty09 As String, ByVal IsTemporaryDeactivated As Boolean, ByVal IsExternal As Boolean, ByVal strCity As String, ByVal crm As String, ByVal strIdentityType As String, ByVal strPersonMasterIdentity As String, ByVal strCentralAccount As String, ByVal br As String, ByVal entryDate As Date) As String

    ' Define o formatador SQL uma vez para reuso
    Const AD_MAPPING_PREFIX As String = "AD-Mapping: "
    Dim sqlFormatter As ISqlFormatter = Connection.SqlFormatter

    ' --- Constantes e Listas de Verificação ---
    ' Ponto de entrada de data para comparação
    Dim entryDateThreshold As Date = Convert.ToDateTime("2022-04-02")

    ' Lista de Company Members que se encaixam na nova regra de data
    Dim companyMemberList As String() = {
        "HOSPITAL SÃO LUCAS", "HOSPITAL BRASÍLIA", "HOSPITAL ÁGUAS CLARAS",
        "HOSPITAL SANTA PAULA", "H9J", "IMPAR", "CHN",
        "SANTA CELINA GI", "SAÚDE CELINA", "INNOVA"
    }

    ' Lista de FullPath de Business Roles que devem retornar o 'br' original
    Dim brExclusiveList As String() = {
        AD_MAPPING_PREFIX & "CHN",
        AD_MAPPING_PREFIX & "SANTA CELINA GI",
        AD_MAPPING_PREFIX & "SAÚDE CELINA",
        AD_MAPPING_PREFIX & "IMPAR",
        AD_MAPPING_PREFIX & "INNOVA",
        AD_MAPPING_PREFIX & "H9J",
        AD_MAPPING_PREFIX & "HOSPITAL SÃO LUCAS",
        AD_MAPPING_PREFIX & "HOSPITAL BRASÍLIA",
        AD_MAPPING_PREFIX & "HOSPITAL SANTA PAULA",
        AD_MAPPING_PREFIX & "SP-EXECUTIVOS"
    }
    
    ' Captura a primeira letra de strCentralAccount apenas uma vez, se necessário
    Dim firstLetter As String = ""
    If Not String.IsNullOrWhiteSpace(strCentralAccount) Then
        firstLetter = Left(strCentralAccount, 1).ToUpper()
    End If

    ' --- Funções Auxiliares ---
    ' Função para obter o UID_Org de um FullPath
    Dim GetOrgUID As Func(Of String, String) = Function(fullPathValue As String)
        Return Connection.GetSingleProperty("Org", "UID_Org", _
            sqlFormatter.comparison("FullPath", fullPathValue, ValType.String, CompareOperator.Equal, FormatterOptions.IgnoreCase))
    End Function

    ' Função para obter um parâmetro de configuração e formatar o FullPath
    Dim GetConfigAndFormatOrgUID As Func(Of String, String) = Function(configParamPath As String)
        Dim configValue As String = Connection.GetConfigParm(configParamPath)
        If Not String.IsNullOrWhiteSpace(configValue) Then
            Return GetOrgUID(AD_MAPPING_PREFIX & configValue)
        End If
        Return ""
    End Function

    Try
        ' --- Lógica Principal ---

        ' 1. Verificação de Usuários Excepcionais/Ajustados (CHAMA O SCRIPT CCC_AjustaUsers)
        Dim adjustedUserUID As String = CCC_AjustaUsers(strCentralAccount)
        If Not String.IsNullOrWhiteSpace(adjustedUserUID) Then
            Return adjustedUserUID
        End If

        ' 2. Regra para Company Members específicos e data de entrada
        If companyMemberList.Contains(strCompanyMember) AndAlso entryDate >= entryDateThreshold Then
            Return GetOrgUID(AD_MAPPING_PREFIX & strCompanyMember)
        End If

        ' 3. Regra para BRs exclusivas (se o FullPath de 'br' estiver na lista)
        Dim fullPathBR As String = Connection.GetSingleProperty("Org", "FullPath", sqlFormatter.uidcomparison("UID_Org", br))
        If brExclusiveList.Contains(fullPathBR) Then
            Return br
        End If

        ' 4. Lógica para Identidades Inativas e Sub-Identidades
        If strIsInactive AndAlso strIdentityType = "Primary" Then
            Return GetOrgUID(AD_MAPPING_PREFIX & "DESABILITADOS")
        Else If strIsInactive AndAlso strIdentityType = "Admin" AndAlso strPersonMasterIdentity.Length > 0 Then
            Select Case firstLetter
                Case "A"
                    Return GetConfigAndFormatOrgUID("Custom\AD\Mapping\DESABILITADOS-CONTA-A")
                Case "O"
                    Return GetConfigAndFormatOrgUID("Custom\AD\Mapping\DESABILITADOS-CONTA-O")
                Case "S"
                    Return GetConfigAndFormatOrgUID("Custom\AD\Mapping\DESABILITADOS-CONTA-S")
            End Select
        Else If Not strIsInactive AndAlso strIdentityType = "Admin" AndAlso strPersonMasterIdentity.Length > 0 Then
            Select Case firstLetter
                Case "A"
                    Return GetConfigAndFormatOrgUID("Custom\AD\Mapping\SUB-IDENTITY-CONTA-A")
                Case "O"
                    Return GetConfigAndFormatOrgUID("Custom\AD\Mapping\SUB-IDENTITY-CONTA-O")
                Case "S"
                    Return GetConfigAndFormatOrgUID("Custom\AD\Mapping\SUB-IDENTITY-CONTA-S")
            End Select
        End If

        ' 5. Lógica para Usuários Externos
        If IsExternal Then
            If crm.Length = 0 Then
                Return GetOrgUID(AD_MAPPING_PREFIX & "TERCEIROS")
            Else
                Return GetOrgUID(AD_MAPPING_PREFIX & "MEDICOS")
            End If
        End If

        ' 6. Lógica para Usuários Afastados Temporariamente
        If IsTemporaryDeactivated AndAlso Not String.IsNullOrWhiteSpace(strCustomProperty09) AndAlso strCustomProperty09.Split(" "c)(0).ToUpper() <> Connection.GetConfigParm("Custom\AD\Mapping\Ferias").ToUpper() Then
            Return GetOrgUID(AD_MAPPING_PREFIX & "AFASTADOS")
        End If

        ' 7. Lógica para Executivos
        If strCustomProperty04.ToUpper() = "EXECUTIVOS" Then
            Dim targetState As String = strDialogState.ToUpper()
            If targetState <> "SP" AndAlso targetState <> "RJ" Then
                targetState = "SP" ' Força SP para estados fora de SP/RJ
            End If
            Return GetOrgUID(AD_MAPPING_PREFIX & targetState & "-" & strCustomProperty04.ToUpper())
        End If

        ' 8. Lógica para Cascavel (TI e Outros)
        If strCity.ToUpper() = "CASCAVEL" Then
            If strCustomProperty06.ToUpper() = "TECNOL DA INFORMAÇÃO" Then
                Dim result As String = GetOrgUID(AD_MAPPING_PREFIX & strCity.ToUpper() & "-" & strCustomProperty06.ToUpper())
                If String.IsNullOrWhiteSpace(result) Then
                    result = GetOrgUID(AD_MAPPING_PREFIX & "SP-DASA-TECNOL DA INFORMAÇÃO")
                End If
                Return result
            Else
                Dim result As String = GetOrgUID(AD_MAPPING_PREFIX & strCity.ToUpper() & "-" & strCompanyMember.ToUpper())
                If String.IsNullOrWhiteSpace(result) Then
                    result = GetConfigAndFormatOrgUID("Custom\AD\Mapping\" & strCity.ToUpper())
                End If
                Return result
            End If
        End If

        ' 9. Lógica para Tecnologia da Informação (TI)
        If strCustomProperty06.ToUpper() = "TECNOL DA INFORMAÇÃO" Then
            Dim result As String = GetOrgUID(AD_MAPPING_PREFIX & strDialogState.ToUpper() & "-DASA-" & strCustomProperty06.ToUpper())
            If String.IsNullOrWhiteSpace(result) Then
                result = GetOrgUID(AD_MAPPING_PREFIX & "SP-DASA-TECNOL DA INFORMAÇÃO")
            End If
            Return result
        End If

        ' 10. Lógica para NAC
        If strCustomProperty06.ToUpper() = "NAC" Then
            Dim result As String = GetOrgUID(AD_MAPPING_PREFIX & strDialogState.ToUpper() & "-" & strCustomProperty06.ToUpper())
            If String.IsNullOrWhiteSpace(result) Then
                result = GetOrgUID(AD_MAPPING_PREFIX & "SP-NAC")
            End If
            Return result
        End If

        ' 11. Lógica Padrão (DASA, Outros e Fallback)
        Dim resultUID As String = ""
        Dim companyMemberCleaned As String = strCompanyMember.Replace(" ", "").ToUpper()

        Select Case companyMemberCleaned
            Case "DASA"
                resultUID = GetOrgUID(AD_MAPPING_PREFIX & strDialogState.ToUpper() & "-" & companyMemberCleaned & "-ADMINISTRATIVO")
                If String.IsNullOrWhiteSpace(resultUID) Then
                    resultUID = GetConfigAndFormatOrgUID("Custom\AD\Mapping\" & companyMemberCleaned)
                End If
            Case Else
                resultUID = GetOrgUID(AD_MAPPING_PREFIX & strDialogState.ToUpper() & "-" & companyMemberCleaned)
                If String.IsNullOrWhiteSpace(resultUID) Then
                    resultUID = GetConfigAndFormatOrgUID("Custom\AD\Mapping\" & companyMemberCleaned)
                End If
                If String.IsNullOrWhiteSpace(resultUID) Then
                    resultUID = GetConfigAndFormatOrgUID("Custom\AD\Mapping\STANDARD") ' Fallback final
                End If
        End Select

        Return resultUID

    Catch ex As Exception
        ' Adiciona informações relevantes ao erro para facilitar a depuração
        Throw New Exception("Erro na função CCC_TPL_PrimaryORG1 para strCentralAccount: " & strCentralAccount & " - Detalhes: " & ex.Message, ex)
    End Try

End Function