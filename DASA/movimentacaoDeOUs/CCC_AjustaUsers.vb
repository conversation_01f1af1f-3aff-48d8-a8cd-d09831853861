Public Function CCC_AjustaUsers(ByVal strCentralAccount As String) As String
    ' Constante para melhorar legibilidade
    Const SP_EXECUTIVOS_PATH As String = "AD-Mapping: SP-EXECUTIVOS"
    
    ' Lista de usuários que precisam ser movidos para SP-EXECUTIVOS
    ' Esta lista foi expandida para incluir todos os usuários que estavam em usuariosIgnorados no CCC_TPL_PrimaryORG1
    Dim usuariosNewOu As String() = {
        "F22653388855", "F51145909876", "F04716017648", "F38816291818", "F46362257839",
        "F49774023838", "F42584964805", "F14314081660", "F48717958881", "F41557568898",
        "F46248297860", "F46314256879", "F40459006835", "F34859719875", "F47986313889",
        "F27065813878", "F47311289866", "F47515654864", "F47750368825", "F08686541402",
        "F50917911830", "F43502668876", "F21304124835", "F35288097879", "F45101581810",
        "F39258975855", "F06427008188", "F15362084852", "F33852230837", "F36142454899",
        "F43502195870", "F02817642392", "F29672003870", "F13456550880", "F20632399830",
        "F34836491827", "F25320369824", "F02921968657", "F28287918820", "F00307098303",
        "F30517786800", "F33329352833", "F32217066854", "F22056723808", "F33740502800",
        "F022********", "F03145083686", "F97723126700", "F81982720620", "F15099551866",
        "F86108409991", "F09486484813", "F40495272949", "F17610943860", "F06328347812",
        "F33773211791", "F02338018710", "F45670161149", "F02214177701", "F72987790104",
        "F01703616596", "F29435515894", "F52905454334", "F28364739832", "S28364739832",
        "S84130083520", "A22964862826", "A70229213189"
    }
    
    ' Verifica se o usuário está na lista de usuários especiais
    If usuariosNewOu.Contains(strCentralAccount) Then
        Dim f As ISqlFormatter = Connection.SqlFormatter
        Return Connection.GetSingleProperty("Org", "UID_Org", _
                f.comparison("FullPath", SP_EXECUTIVOS_PATH, ValType.String, CompareOperator.Equal, FormatterOptions.IgnoreCase))
    End If
    
    ' Retorna string vazia se o usuário não estiver na lista
    Return ""
End Function
