Public Sub CCC_Gliese_DisableAccount(ByVal UID_UNSAccount As String)

Dim user As String = UID_UNSAccount
' Define the JSON data to send  
Dim jsonData As String = String.Format("{{ ""usuario"": {{ ""identificacao"": ""{0}"", ""status"": ""1"" }} }}", user) 
  
' Create a new WebClient object  
Dim client As New System.Net.WebClient()  
  
' Set the content type header to indicate JSON data  
client.Headers.Add("Content-Type", "application/json")
client.Headers.Add("apikey", "IQRpKNKodzLzAp02J1hwYCkBDRADCwXL")
  
' Send the POST request and get the response data  
Dim responseBytes As Byte() = client.UploadData("https://api.godigibee.io/pipeline/dasa/v1/gliese-usuario", "PATCH", System.Text.Encoding.ASCII.GetBytes(jsonData))  
Dim responseString As String = System.Text.Encoding.ASCII.GetString(responseBytes)  
  

End Sub