Public Sub CCC_Custom_SQDASA(ByVal identificacao As String)
	Imports Newtonsoft.Json.Linq

Dim f As ISqlFormatter = Connection.SqlFormatter
Dim departmentId As String = identificacao

	Dim jsonData As String = String.Format("{{""usuario"": ""SVC_IDM_SQDASA"", ""senha"": ""lQ@y862v7O3Xyn%1@y""}}")  
	Dim client As New System.Net.WebClient()
	client.Headers.Add("Content-Type", "application/json")
	Dim responseBytes As Byte() = client.UploadData("http://10.122.50.50:2020/api/v1/auth/authenticate", "POST", System.Text.Encoding.ASCII.GetBytes(jsonData))  
	Dim responseString As String = System.Text.Encoding.ASCII.GetString(responseBytes)  

	Dim jsonObject As JObject = JObject.Parse(responseString)  
	Dim accessToken As String = jsonObject("token").ToString() 

		Dim jsonDataSQDASA As String = String.Format("{{""usuarios"": [""{0}""] }}", identificacao)

		Dim clientNav As New System.Net.WebClient()
		clientNav.Headers.Add("Content-Type", "application/json")
		clientNav.Headers.Add("Authorization", "Bearer " + accessToken)
		Dim responseBytesNav As Byte() = clientNav.UploadData("http://10.122.50.50:2020/api/v1/usuario/sincronizarAD", "POST", System.Text.Encoding.ASCII.GetBytes(jsonDataSQDASA))  
		Dim responseStringNav As String = System.Text.Encoding.ASCII.GetString(responseBytesNav)  
		
End Sub