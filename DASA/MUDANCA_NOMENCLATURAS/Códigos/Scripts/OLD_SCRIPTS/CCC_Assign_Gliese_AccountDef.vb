Dim <PERSON>gos As String() = { "Recepcionista I",
"Recepcionista II",
"Recepcionista Premium I",
"Recepcionista Premium I",
"Recepcionista Coleta Consultorio I",
"Recepcionista",
"Recepcionista de Und de Atd",
"Recepcionista Coleta Consultorio II",
"Recepcionista Coleta",
"Recepcionista de Atendimento",
"Recepcionista Faturista",
"Recepcionista Coleta Iii",
"Recepcionista - Santa Luzia",
"Recepcionista Coleta I",
"Recepcionista Tecnico",
"Recepcionista Hospitalar I",
"Recepcionista Hospitalar Premium I",
"Recepcionista Hospitalar II",
"Recepcionista Hospitalar",
"Recepcionista Líder",
"Recepcionista Premium Líder",
"Recepcionista Lider",
"Recepcionista VIP",
"Recepcionista Lider Coleta Consultorio",
"Recepcionista Hospitalar Líder",
"Técnico Coleta I",
"Assistente Coleta I",
"Técnico Coleta II",
"Técnico Coleta III",
"Tecnico Coleta II",
"Técnico Coleta Unidade",
"Técnico Coleta Premium II",
"Tecnico de Coleta II",
"Tecnico Coleta I",
"Assistente Coleta Unidade",
"Lider Coleta Unidade",
"Assistente Coleta II",
"Assistente de Coleta II",
"Tecnico de Coleta I",
"Tecnico Coleta III",
"Assistente de Coleta I",
"Coletador",
"Técnico Coleta Premium III",
"Coletador Jr",
"Assistente Coleta Premium I",
"Tecnico Coleta Unidade Premium",
"Coleta",
"Técnico de Coleta I",
"Tecnico de Coleta III",
"Tecnico Coleta Unidade",
"Coletador Líder",
"Coletador Hospitalar",
"Unidade II Coleta",
"Coletadora",
"Auxiliar de Coleta",
"Ap Coleta",
"Tecnica de Coleta",
"Auxiliar de Coleta I",
"Central Central Coleta",
"Líder Coleta Unidade Premium",
"Coletador Lider",
"Lider Coleta",
"Tecnico de Coleta Unidade",
"Unidade X Unidade X Coleta",
"TÉCNICO DE COLETA II",
"Recepcao Coleta",
"Técnico Exame Premium RDI III",
"Técnico Exame Premium RDI II",
"Tecnico Exame Rdi III",
"Técnico Exame RDI III",
"Técnico Exame RDI IV",
"Tecnico de Exame Rdi Iii",
"Técnico Exame Premium RDI I",
"Tecnico Exame Rdi II",
"Técnico Exame RDI II",
"Técnico Exame Premium RDI IV",
"Tecnico Exame Rdi Iv",
"Coordenador Unidade I",
"Coordenador Unidade Hospitais I",
"Coordenador Unidade II",
"Coordenador Unidade III",
"Coordenador Unidade Coleta",
"Coordenador Unidade Coleta Consultorio",
"Coordenador Unidade Tecnico",
"Coordenador Unidade IV",
"Coordenador Unidade Hospitais II",
"Coordenador Unidade Atendimento",
"Coordenador Unidade Pesquisa Clinica",
"Coordenador Unidade Coleta II",
"Coordenador de Unidade",
"Coordenador de Unidade I",
"Coordenador de Unidade Iii",
"Coordenador de Unidade Iv",
"Coordenador de Unidade Ii",
"Assistente Sala",
"Assistente De Sala",
"AUXILIAR DE SALA",
"Apoiadora de Sala"}
Dim Empresas As String() = {
"HOSPITAL SÃO LUCAS","MATERNIDADE BRASÍLIA","HOSPITAL BRASÍLIA","HOSPITAL ÁGUAS CLARA","HOSPITAL SANTA PAULA","H9J","IMPAR","CHN","SANTA CELINA GI","SAÚDE CELINA","INNOVA"
}
 Value = Cargos.Contains($PersonalTitle$) AND $IdentityType$ = "Primary" AND $IsInActive:Bool$ = False AND NOT Empresas.Contains($CompanyMember$)