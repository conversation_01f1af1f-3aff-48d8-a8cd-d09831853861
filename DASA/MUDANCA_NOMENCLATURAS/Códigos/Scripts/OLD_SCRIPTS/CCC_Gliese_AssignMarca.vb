Imports Newtonsoft.Json.Linq  

Public Sub CCC_Gliese_AssignMarca(ByVal identificacao As String, ByVal marca As String)
'	e0a2b68f-aafe-4a8b-8119-15fc4007f068
Dim f = Session.SqlFormatter
Dim identificacaoS As String = identificacao
Dim marcaS As String = marca
Dim objectSS As String


	Dim personManagerAdsAccountQuery = f.<PERSON>("CentralAccount", identificacaoS)
    Dim personManagerAdsAccountCol As IEntityCollection = Session.Source().GetCollection(Query.From("Person").Where(personManagerAdsAccountQuery).SelectNonLobs)
	Dim personManagerAdsAccount As IEntity = personManagerAdsAccountCol(0)
	Dim personManagerAdsAccountQuery2 = f.AndRelation(f.UidComparison("UID_Person", personManagerAdsAccount.GetValue("UID_Person").String),f.<PERSON>("UID_UNSRootB", "e0a2b68f-aafe-4a8b-8119-15fc4007f068"))
    Dim personManagerAdsAccountCol2 As IEntityCollection = Session.Source().GetCollection(Query.From("UNSAccountB").Where(personManagerAdsAccountQuery2).SelectNonLobs)
	Dim personManagerAdsAccount2 As IEntity = personManagerAdsAccountCol2(0)
	If personManagerAdsAccount2.GetValue("ObjectGUID").String <> "" Then
	objectSS = personManagerAdsAccount2.GetValue("ObjectGUID").String.Replace("GLIESE_", "")
	identificacaoS = objectSS
End If

'msgbox(identificacao)

 'Define the JSON data To send  
'Dim jsonData As String = "{ ""usuario"": { ""identificacao"": " + user + ", ""menus"": [" + groups + "] } }" 
Dim jsonData As String = String.Format("{{""isConsulta"" : ""true"", ""usuario"": {{ ""identificacao"": ""{0}""}} }}", identificacaoS) 
'

 'Create a New WebClient Object  
Dim client As New System.Net.WebClient()  
  
 'Set the content type header To indicate JSON data  
client.Headers.Add("Content-Type", "application/json")
client.Headers.Add("apikey", "IQRpKNKodzLzAp02J1hwYCkBDRADCwXL")
  
 'Send the POST request And Get the response data  
Dim responseBytes As Byte() = client.UploadData("https://api.godigibee.io/pipeline/dasa/v1/gliese-usuario", "POST", System.Text.Encoding.ASCII.GetBytes(jsonData))  
Dim responseString As String = System.Text.Encoding.ASCII.GetString(responseBytes)  

Dim jsonObj As JObject = JObject.Parse(responseString)  
Dim identificacaoC As String = jsonObj.SelectToken("data[0].identificacao").ToString()

Dim matriculaC As String = jsonObj.SelectToken("data[0].matricula").ToString()
Dim cpfC As String = jsonObj.SelectToken("data[0].cpf").ToString()
Dim nomeC As String = jsonObj.SelectToken("data[0].nome").ToString()
Dim emailC As String = jsonObj.SelectToken("data[0].email").ToString()
Dim funcaoC As String = jsonObj.SelectToken("data[0].funcao").ToString()
Dim setorC As String = jsonObj.SelectToken("data[0].setor").ToString()
Dim dataExpiracaoC As String = jsonObj.SelectToken("data[0].dataExpiracao").ToString()
Dim empresaInternaC As String = jsonObj.SelectToken("data[0].empresaInterna").ToString()
Dim mnemonicoPostoC As String = jsonObj.SelectToken("data[0].mnemonicoPosto").ToString()
Dim unidadeRealizacaoExameC As String = jsonObj.SelectToken("data[0].unidadeRealizacaoExame").ToString()
Dim empresaS As String = jsonObj.SelectToken("data[0].empresasIncorporadas").ToString()
'msgbox(empresaS)
empresaS = empresaS.Replace("]", "")
empresaS = empresaS.Replace("[", "")
empresaS = empresaS.Replace("""", "")
empresaS = empresaS.Replace(" ", "")
empresaS = Replace(empresaS, vbCr, "")
empresaS = Replace(empresaS, vbLf, "")
'msgbox(empresaS)

If String.IsNullOrEmpty(empresaS) Then
empresaS = marcaS
Else
empresaS = empresaS + "," + marcaS
End If

' Divida a string em um array de strings
Dim empresasIncorporadasArray() As String = empresaS.Split(","c)
        
' Remova duplicados usando um HashSet
Dim empresasIncorporadasSet As New HashSet(Of String)(empresasIncorporadasArray)
        
' Converta o HashSet de volta para um array
Dim empresasIncorporadasUnicasArray() As String = empresasIncorporadasSet.ToArray()
        
' Serializa o array para JSON
Dim empresasIncorporadasJson As String = JsonConvert.SerializeObject(empresasIncorporadasUnicasArray)


jsonData = String.Format("{{ ""usuario"": {{ ""identificacao"": ""{0}"", ""matricula"": ""{1}"", ""nome"": ""{2}"", ""funcao"": ""{3}"", ""setor"": ""{4}"", ""unidadeRealizacaoExame"": ""{5}"", ""email"": ""{6}"", ""empresaInterna"": ""{7}"", ""cpf"": ""{8}"", ""mnemonicoPosto"": ""{9}"", ""dataExpiracao"": ""{10}"", ""empresasIncorporadas"": {11} }} }}", identificacaoS, matriculaC, nomeC, funcaoC, setorC, unidadeRealizacaoExameC, emailC, empresaInternaC, cpfC, mnemonicoPostoC, dataExpiracaoC, empresasIncorporadasJson)

'msgbox(jsonData)

' Create a new WebClient object  
'msgbox(jsonData)
  Dim client2 As New System.Net.WebClient()
 'Set the content type header To indicate JSON data  
client2.Headers.Add("Content-Type", "application/json")
client2.Headers.Add("apikey", "IQRpKNKodzLzAp02J1hwYCkBDRADCwXL")
  
 'Send the POST request And Get the response data  
Dim responseBytes2 As Byte() = client2.UploadData("https://api.godigibee.io/pipeline/dasa/v1/gliese-usuario", "PATCH", System.Text.Encoding.ASCII.GetBytes(jsonData))  
Dim responseString2 As String = System.Text.Encoding.ASCII.GetString(responseBytes2) 

End Sub