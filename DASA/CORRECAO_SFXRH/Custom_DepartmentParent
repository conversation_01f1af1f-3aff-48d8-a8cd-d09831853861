'#If Not SCRIPTDEBUGGER Then
Imports System
References VI.DB.Entities
#End If

Public Sub SetDepartmentParent()

    Try
        'Nome do departamento para o qual estamos configurando o departamento pai.
        Dim departmentName As String = "Finance" ' Substitua pelo nome do departamento atual, se disponível

        'Função para obter o nome do departamento pai
        Dim parentDepartmentName As String = GetParentDepartmentName(departmentName)
        
        'Atualiza CustomProperty01 com o nome do departamento pai, se encontrado
        If Not String.IsNullOrEmpty(parentDepartmentName) Then
            'Simulação de atualização de campo (replace com lógica de update do banco de dados se disponível)
            Dim customProperty01 As String = parentDepartmentName
            Console.WriteLine("CustomProperty01 atualizado para: " & customProperty01)
        End If

    Catch ex As Exception
        'Log simplificado em console para ambientes sem o objeto Session
        Console.WriteLine("Erro ao definir o departamento pai: " & ex.Message)
    End Try
End Sub