'CORRECAO DO TEMPLATE DE CENTRALACCOUNT NA PERSON

If (CStr($ImportSource$) <> "ADS" And _
    (Not CBool(Connection.Variables.Get("FULLSYNC")) Or CStr($ImportSource$).StartsWith("EBS") Or CStr($ImportSource$).StartsWith("SAP"))) _
    Or _
    (CBool(Connection.Variables.Get("FULLSYNC")) And $IdentityType$ = "Primary" And $IsExternal$ = "False") Then
    If CStr(Connection.GetConfigParm("QER\Person\CentralAccountGlobalUnique"))="1" Then
        Value = VI_AE_BuildCentralAccountGlobalUnique( GetValue("UID_Person").String, $Lastname$, $Firstname$)
    Else
        If $IdentityType$ = "Primary" Then
            Value = CCC_AE_BuildCentralAccount( GetValue("UID_Person").String, $CustomProperty02$, $CustomProperty10$, $IsExternal:Bool$)
        End If
    End If
End If

'COLUNAS ADSACCOUNT

    'AccountDisabled

If (Not CBool(Variables("FULLSYNC")) Or _
(CBool(Variables("FULLSYNC")) And ($FK(UID_Person).ImportSource$).StartsWith("SF_HR"))) Then
	If $UID_PERSON$ <> "" Andalso CCC_TempDisablingValidation($FK(UID_Person).Customproperty04$, $FK(UID_Person).Customproperty06$, $FK(UID_Person).IsInActive:Bool$, $FK(UID_Person).PersonalTitle$) Then
		If $UID_TSBBehavior$<>"" Then
			'The account is in a managed domain, the flag depends on the resource switch for the respective manage level
			' only if the corresp. ..LockAccount flag for the resp. manage level is set at all, is the value set for the resp. employee property
			If _
					( $FK(UID_TSBBehavior).PMDLockAccount:bool$ AndAlso ($FK(uid_person).XMarkedForDeletion:Int$ And 1) = 1) _
				Or _
					( $FK(UID_TSBBehavior).PFDLockAccount:bool$ AndAlso $FK(uid_person).IsInActive:bool$) _
				Or _ 
					( $FK(UID_TSBBehavior).PTDLockAccount:bool$ AndAlso $FK(uid_person).IsTemporaryDeactivated:bool$) _
				Or _
					( $FK(UID_TSBBehavior).PSILockAccount:bool$ AndAlso $FK(uid_person).IsSecurityIncident:bool$) _
			Then
				Value = True
			
			ElseIf _
					( $FK(UID_TSBBehavior).PMDLockAccount:bool$ AndAlso ($FK(uid_person).XMarkedForDeletion:Int$ And 1) <> 1) _
				Or _
					( $FK(UID_TSBBehavior).PFDLockAccount:bool$ AndAlso Not $FK(uid_person).IsInActive:bool$) _
				Or _ 
					( $FK(UID_TSBBehavior).PTDLockAccount:bool$ AndAlso Not $FK(uid_person).IsTemporaryDeactivated:bool$) _
				Or _
					( $FK(UID_TSBBehavior).PSILockAccount:bool$ AndAlso Not $FK(uid_person).IsSecurityIncident:bool$) _
			Then
				Value = False
			End If
			
		ElseIf CStr(Connection.GetConfigParm("QER\Person\TemporaryDeactivation"))="1" Then
			' The account is not managed and the configparm TemporaryDeactivation is set
			Value = ($FK(uid_person).IsTemporaryDeactivated:Bool$ Or $FK(UID_Person).IsInActive:Bool$)
		End If
	Else
		If cbool(ctype($XMarkedForDeletion:Int$,VI.DB.XMarkedForDeletion) And VI.DB.XMarkedForDeletion.DeletionMask) Then
			' #21618, account will be disabled
			Value = true
		Else
			If cbool(ctype($XMarkedForDeletion[o]:Int$,VI.DB.XMarkedForDeletion) And VI.DB.XMarkedForDeletion.DeletionMask) Then
			' #21618, account is not assigned to an employee and is recalled
				Value = False
			End If
		End If
	End If
End If

    'AccountExpires

If (Not CBool(Variables("FULLSYNC")) Or _
(CBool(Variables("FULLSYNC")) And ($FK(UID_Person).ImportSource$).StartsWith("SF_HR"))) Then
Select Case ($FK(UID_TSBBehavior).ITDataUsage:Int$)
	Case 0:'do not get data from employee
	Case -1:'fill property initially from the ITData of the employee
		If Not $[IsLoaded]:Bool$ Then
			'always end of the day 23:59
			'Value = DateValue(CStr($FK(UID_Person).DateLastWorked:Date$)).Date.AddMinutes(1439)
			Dim localDate As DateTime = DateValue(CStr($FK(UID_Person).DateLastWorked:Date$)).Date.AddMinutes(1439)
			Dim utcDate As DateTime = localDate.ToUniversalTime()
			Value = utcDate
		End If
	Case 1:'update property depending on ITData of the employee
		'always end of the day 23:59
		'Value = DateValue(CStr($FK(UID_Person).DateLastWorked:Date$)).Date.AddMinutes(1439)
		Dim localDate As DateTime = DateValue(CStr($FK(UID_Person).DateLastWorked:Date$)).Date.AddMinutes(1439)
		Dim utcDate As DateTime = localDate.ToUniversalTime()
			Value = utcDate
End Select
End If

    'AllowLogonTerminalServer

If (Not CBool(Variables("FULLSYNC")) Or _
(CBool(Variables("FULLSYNC")) And ($FK(UID_Person).ImportSource$).StartsWith("SF_HR"))) Then
	Select Case ($FK(UID_TSBBehavior).ITDataUsage:Int$)
		Case 0:'do not get data from employee
		Case -1:'fill property initially from the ITData of the employee
			If Not $[IsLoaded]:Bool$ Then
				Value = True
			End If
		Case 1:'update property depending on ITData of the employee
			Value = True
	End Select
End If

    'cn

If (Not CBool(Variables("FULLSYNC")) Or _
(CBool(Variables("FULLSYNC")) And ($FK(UID_Person).ImportSource$).StartsWith("SF_HR"))) Then
	Dim CN, CNfix, DNold, DNnew,Domain, wherePart As String
	Dim i As Integer
	Dim f As ISqlFormatter = Connection.SqlFormatter

	If $Givenname$ <> $Givenname[o]$ Or $Surname$ <> $Surname[o]$ Then
		If $Givenname$<>"" AndAlso $Surname$<>"" Then
			CN = $Givenname$ & " " & $Surname$
		ElseIf $Givenname$<>"" Then
			CN = $Givenname$
		ElseIf $Surname$<>"" Then
			CN = $Surname$
		Else
			CN = $cn$
		End If
	Else
		CN = $cn$
	End If

	Dim cnLen as Integer = Base.TableDef.Columns("CN").MaxLen
	CN = VID_Left(CN, cnLen)

	If $cn[o]$<>CN Or $UID_ADSContainer[o]$<>$UID_ADSContainer$ Then
		i=1
		CNFix = CN
		If $UID_ADSContainer[o]$ <> "" Then
			DNold = $FK(UID_ADSContainer[o]).DistinguishedName$
		Else
			DNold = $FK(UID_ADSDomain[o]).DistinguishedName$
		End If
		If $UID_ADSContainer$ <> "" Then
			DNnew = $FK(UID_ADSContainer).DistinguishedName$
		Else
			DNnew = $FK(UID_ADSDomain).DistinguishedName$
		End If
		Domain = $UID_ADSDomain$

		If DNold <> "" And DNold <> DNnew Then
			wherePart = f.OrRelation( _
							f.Comparison("DistinguishedName", ADS_CreateDN("cn", CN, DNnew), ValType.String), _
							f.Comparison("DistinguishedName", ADS_CreateDN("cn", CN, DNold), ValType.String) _
								)
		Else
			wherePart =	f.Comparison("DistinguishedName", ADS_CreateDN("cn", CN, DNnew), ValType.String)
		End If
							
		Do While Connection.Exists(	"ADSAccount", _
				f.AndRelation( _
						f.UidComparison("UID_ADSAccount", $UID_ADSAccount$, CompareOperator.NotEqual), _
						f.UidComparison("UID_ADSDomain", Domain), _
						wherePart _
							))

			CN = VID_Left(CNfix, cnLen - (1 + CStr(i).Length)) & "_" & i
			
			If DNold <> "" And DNold <> DNnew Then
				wherePart = f.OrRelation( _
								f.Comparison("DistinguishedName", ADS_CreateDN("cn", CN, DNnew), ValType.String), _
								f.Comparison("DistinguishedName", ADS_CreateDN("cn", CN, DNold), ValType.String) _
								)
			Else
				wherePart =	f.Comparison("DistinguishedName", ADS_CreateDN("cn", CN, DNnew), ValType.String)
			End If

			i=i+1
		Loop
	End If
	Value = CN
End If

    'Department

If (Not CBool(Variables("FULLSYNC")) Or _
(CBool(Variables("FULLSYNC")) And ($FK(UID_Person).ImportSource$).StartsWith("SF_HR"))) Then
	Select Case ($FK(UID_TSBBehavior).ITDataUsage:Int$)
		Case 0:'do not get data from employee
		Case -1:'fill property initially from the ITData of the employee
			If Not $[IsLoaded]:Bool$ Then
				'Value = $FK(UID_Person).FK(UID_Department).ShortName$
				Value = $FK(UID_Person).FK(UID_Department).DepartmentName$ & " - " & $FK(UID_Person).FK(UID_Department).ShortName$
			End If
		Case 1:'update property depending on ITData of the employee
			'Value = $FK(UID_Person).FK(UID_Department).ShortName$
			Value = $FK(UID_Person).FK(UID_Department).DepartmentName$ & " - " & $FK(UID_Person).FK(UID_Department).ShortName$
	End Select
End If

    'Description

If (Not CBool(Variables("FULLSYNC")) Or _
(CBool(Variables("FULLSYNC")) And ($FK(UID_Person).ImportSource$).StartsWith("SF_HR"))) Then
	Select Case ($FK(UID_TSBBehavior).ITDataUsage:Int$)
		Case 0:'do not get data from employee
		
		Case -1:'fill property initially from the ITData of the employee
			If Not $[IsLoaded]:Bool$ Then
				if $IsPrivilegedAccount:Bool$ then
					Value = "Usuario Administrativo Tecnologia"
				else
					Value = $FK(UID_Person).Description$
				End if
			End If
		Case 1:'update property depending on ITData of the employee
			if $IsPrivilegedAccount:Bool$ then
				Value = "Usuario Administrativo Tecnologia"
			else
				Value = $FK(UID_Person).Description$
			End if	
	End Select
End If

    'DisplayName

#If Comment Then
	If Not CBool(Variables("FULLSYNC")) AndAlso ($GivenName[o]$ <> $GivenName$ OrElse $Surname[o]$ <> $Surname$) Then
		If $Givenname$<>"" AndAlso $Surname$<>"" Then
		    Value = $Givenname$ & " " & $Surname$ 
		ElseIf $Givenname$<>"" Then
		    Value = $Givenname$
		ElseIf $Surname$<>"" Then
		    Value = $Surname$
		End If
	End If
#End If

If (Not CBool(Variables("FULLSYNC")) Or _
(CBool(Variables("FULLSYNC")) And ($FK(UID_Person).ImportSource$).StartsWith("SF_HR"))) Then
	If Not String.IsNullOrWhiteSpace($FK(UID_Person).PreferredName$) Then
		Value = $FK(UID_Person).PreferredName$
	Else
		If ($GivenName[o]$ <> $GivenName$ OrElse $Surname[o]$ <> $Surname$) Then
			If $Givenname$<>"" AndAlso $Surname$<>"" Then
			    Value = $Givenname$ & " " & $Surname$ 
			ElseIf $Givenname$<>"" Then
			    Value = $Givenname$
			ElseIf $Surname$<>"" Then
			    Value = $Surname$
			End If
		End If
	End If
End If

    'ExtensionAttribute5

If (Not CBool(Variables("FULLSYNC")) Or _
(CBool(Variables("FULLSYNC")) And ($FK(UID_Person).ImportSource$).StartsWith("SF_HR"))) Then
	Select Case ($FK(UID_TSBBehavior).ITDataUsage:Int$)
		Case 0:'do not get data from employee
		Case -1:'fill property initially from the ITData of the employee
			If $FK(UID_Person).IdentityType$ = "Primary" Then
				Value = $FK(UID_Person).PhoneMobile:Text$
			End If
		Case 1:'update property depending on ITData of the employee
			If $FK(UID_Person).IdentityType$ = "Primary" Then
				Value = $FK(UID_Person).PhoneMobile:Text$
			End If
	End Select
End If

    'GivenName

If Not String.IsNullOrWhiteSpace($FK(UID_Person).PreferredName$) Then
	Value = CCC_CheckName("fn",$FK(UID_Person).PreferredName$)
	
Else If (Not CBool(Variables("FULLSYNC")) Or _
(CBool(Variables("FULLSYNC")) And ($FK(UID_Person).ImportSource$).StartsWith("SF_HR"))) Then
	Select Case ($FK(UID_TSBBehavior).ITDataUsage:Int$)
		Case 0:'do not get data from employee
		Case -1:'fill property initially from the employee
			If Not $[IsLoaded]:Bool$ Then
				Value = $FK(UID_Person).Firstname$
			End If
		Case 1:'update property depending on employee
			Value = $FK(UID_Person).Firstname$
	End Select
End If

    'IdentityType

'$FK(UID_Person).UID_Department$
'$FK(UID_Person).UID_Locality$
'$FK(UID_Person).UID_ProfitCenter$
#If ORG Then
'$FK(UID_Person).UID_Org$
#End If
If (Not CBool(Variables("FULLSYNC")) Or _
(CBool(Variables("FULLSYNC")) And ($FK(UID_Person).ImportSource$).StartsWith("SF_HR"))) Then
Select Case ($FK(UID_TSBBehavior).ITDataUsage:Int$)
	Case 0:'do not get data from employee
	Case -1:'fill property initially from the ITData of the employee
		If Not $[IsLoaded]:Bool$ Then
			Value = TSB_ITDataFromOrg($UID_Person$, $UID_TSBAccountDef$, Base.TableDef.Columns("IdentityType")) 
		End If
	Case 1:'update property depending on ITData of the employee
		Value = TSB_ITDataFromOrg($UID_Person$, $UID_TSBAccountDef$, Base.TableDef.Columns("IdentityType")) 
End Select
End If

    'IsGroupAccount

'$FK(UID_Person).UID_Department$
'$FK(UID_Person).UID_Locality$
'$FK(UID_Person).UID_ProfitCenter$
#If ORG Then
'$FK(UID_Person).UID_Org$
#End If
If (Not CBool(Variables("FULLSYNC")) Or _
(CBool(Variables("FULLSYNC")) And ($FK(UID_Person).ImportSource$).StartsWith("SF_HR"))) Then
Select Case ($FK(UID_TSBBehavior).ITDataUsage:Int$)
	Case 0:'do not get data from employee
	Case -1:'fill property initially from the ITData of the employee
		If Not $[IsLoaded]:Bool$ Then
			Value = VID_IsTrue(TSB_ITDataFromOrg($UID_Person$, $UID_TSBAccountDef$, Base.TableDef.Columns("IsGroupAccount")))
		End If
	Case 1:'update property depending on ITData of the employee
		Dim f As ISqlFormatter = Connection.SqlFormatter
		If Session.Source().Exists("TSBITDataMapping", _
						f.AndRelation( _
				                f.UidComparison("UID_TSBAccountDef", $UID_TSBAccountDef$), _
				                f.UidComparison("UID_DialogColumn", Base.TableDef.Columns("IsGroupAccount").Uid))) Then
			'overwrite only when the column IsGroupAccount is mapped
			Value = VID_IsTrue(TSB_ITDataFromOrg($UID_Person$, $UID_TSBAccountDef$, Base.TableDef.Columns("IsGroupAccount")))
		End If
End Select
End If

    'IsPrivilegedAccount

'$FK(UID_Person).UID_Department$
'$FK(UID_Person).UID_Locality$
'$FK(UID_Person).UID_ProfitCenter$
#If ORG Then
'$FK(UID_Person).UID_Org$
#End If
If (Not CBool(Variables("FULLSYNC")) Or _
(CBool(Variables("FULLSYNC")) And ($FK(UID_Person).ImportSource$).StartsWith("SF_HR"))) Then
Select Case ($FK(UID_TSBBehavior).ITDataUsage:Int$)
	Case 0:'do not get data from employee
	Case -1:'fill property initially from the ITData of the employee
		If Not $[IsLoaded]:Bool$ Then
			Value = VID_IsTrue(TSB_ITDataFromOrg($UID_Person$, $UID_TSBAccountDef$, Base.TableDef.Columns("IsPrivilegedAccount")))
		End If
	Case 1:'update property depending on ITData of the employee
		Value = VID_IsTrue(TSB_ITDataFromOrg($UID_Person$, $UID_TSBAccountDef$, Base.TableDef.Columns("IsPrivilegedAccount")))
End Select
End If

    'Mail

If (Not CBool(Variables("FULLSYNC")) Or _
(CBool(Variables("FULLSYNC")) And ($FK(UID_Person).ImportSource$).StartsWith("SF_HR"))) Then
	Select Case ($FK(UID_TSBBehavior).ITDataUsage:Int$)
		Case 0:'Unmanaged (do not get data from employee)
'#If EX2K Then
			
'			Dim mailaddress As String = $Mail$
			
'			If  mailaddress="" AndAlso $SAMAccountName$<>"" Then 
				'if no mailaddress is set manually, generate
'				Dim account As String = VI_AE_FormatConvertUmlaut_Sonderzeichen($SAMAccountName$)
'				Dim maildom As String = Connection.GetConfigParm("QER\Person\DefaultMailDomain")
'				
'				If maildom <> "" Then
 '               	 ' default mail domain has to be defined, see configuration parameter 'QER\Person\DefaultMailDomain'
  '          		If Not maildom.StartsWith("@") Then
   '                 	maildom = "@" & maildom
    '            	End If
 	'				mailaddress = account & maildom
	'			End If
	'		End If
'
'			If $Mail[o]$ <> mailaddress AndAlso mailaddress <> "" Then 
'				'make the new mailaddress unique by appending a counter
'				Dim mailParts As String() = mailaddress.Split("@"c)
'
'				Dim i as Integer = 0
'		        Dim f As ISqlFormatter = Connection.SqlFormatter
'
'		        While Connection.Exists("ADSAccount", _
'									f.AndRelation( _
'											f.UidComparison("UID_ADSAccount", $UID_ADSAccount$, CompareOperator.NotEqual), _
'											f.Comparison("Mail", mailaddress, ValType.String, CompareOperator.Equal, FormatterOptions.None) _
'												)) _
'						OrElse Connection.Exists("ADSContact", _
'									f.Comparison("Mail", mailaddress, ValType.String, CompareOperator.Equal, FormatterOptions.None) _
'												)
'					i += 1
''					mailaddress = mailParts(0) & i.ToString() & "@" & mailParts(1)
	'				If mailaddress.Length > 255 Then 
	'					mailaddress = mailParts(0).Substring(0, 255-i.ToString().Length) & i.ToString() & "@" & mailParts(1)
	'				End If
	'				
	'				If i = 100000 'termination
	'					Exit While
	'				End If
'
'				End While
'			
'				Value = mailaddress

'			End If
'#End If	
		Case -1:'fill property initially from the employee
			If Not $[IsLoaded]:Bool$ Then
				if $IsPrivilegedAccount:Bool$ Then
					Value = ""
				else
					Value = $FK(UID_Person).DefaultEMailAddress$.ToLower
				end if
			End If
		Case 1:'update property depending on employee
			if $IsPrivilegedAccount:Bool$ Then
				Value = ""
			else
				Value = $FK(UID_Person).DefaultEMailAddress$.ToLower
			end if
	End Select
End If

    'SAMAccountName

'$SAMAccountName$ - trigger test for manual update
If (Not CBool(Variables("FULLSYNC")) Or _
(CBool(Variables("FULLSYNC")) And ($FK(UID_Person).ImportSource$).StartsWith("SF_HR"))) Then
	Dim Acc As String = ""
	Dim MyString As String = ""
	Dim i As Integer = 0
	Dim AccExist As Boolean = False
	Dim txt As New System.Text.StringBuilder(Acc.Length)
	Dim f As ISqlFormatter = Connection.SqlFormatter
	Dim prefixAccount as String = connection.GetConfigParm("TargetSystem\ADS\Accounts\PrivilegedAccount\SAMAccountName_Prefix")
	Dim postfixAccount as String = connection.GetConfigParm("TargetSystem\ADS\Accounts\PrivilegedAccount\SAMAccountName_Postfix")

	Select Case ($FK(UID_TSBBehavior).ITDataUsage:Int$)
		Case 0:'do not get data from employee
			'only initially and if the operator has not set the SAMAccountName manually
			If Not $[IsLoaded]:Bool$ AndAlso $cn$<>"" AndAlso Not Entity.Columns.Item("SAMAccountName").ChangeLevels.Get(0) Then
				If $IsPrivilegedAccount:Bool$ Then
					Acc = Left(prefixAccount & Left($cn$, 18-prefixAccount.Length-postfixAccount.Length) & postfixAccount, 18)
				Else
					Acc = Left($cn$, 18)
				End If
			Else
				Acc = CStr(Value)
			End If
		Case -1:'fill property initially depending on the employee
			If Not $[IsLoaded]:Bool$ Then
				If $IsPrivilegedAccount:Bool$ Then
					'Remove caracter inicial F ou T
					MyString = $FK(UID_Person).CentralAccount$
					Acc = Left(prefixAccount & Left(MyString.TrimStart("F"c,"T"c), 20-prefixAccount.Length-postfixAccount.Length) & postfixAccount, 20)
				Else
					Acc = Left($FK(UID_Person).CentralAccount$,20) 
				End if
			Else
				Acc = CStr(Value)
			End If
		Case 1:'update property depending on the employee
			If $IsPrivilegedAccount:Bool$ Then
				MyString = $FK(UID_Person).CentralAccount$
				Acc = Left(prefixAccount & Left(MyString.TrimStart("F"c,"T"c), 20-prefixAccount.Length-postfixAccount.Length) & postfixAccount, 20)
			Else
				Acc = Left($FK(UID_Person).CentralAccount$,20) 
			End if
	End Select

	If Acc <> "" Then
		' Invalid characters in Samaccountname are replaced with _
		' /\[]:;|=,+*?<>"@
		For Each c As Char In Acc
			Select Case c
				Case "/"c, "\"c, "["c, "]"c, ":"c, ";"c, "|"c, "="c, ","c, "+"c, "*"c, "?"c, "<"c, ">"c, """"c, "@"c
					txt.Append("_"c)
				Case Else
					txt.Append(c)
			End Select
		Next
		Acc = txt.ToString()
		
		'character . at the end of Samaccountname is invalid
		If Right(Acc,1)="."c Then
			Acc = Acc.Substring(0,Acc.Length-1)
		End If
		
		'prevents triggering the template on changes to the ADSDomain (use ObjectWalker)
		Dim Domain As String = GetValue("UID_ADSDomain").String
			
		Select Case ($FK(UID_TSBBehavior).ITDataUsage:Int$)
			Case 0:'do not get data from employee (if this account is already in the domain then increment)
				Do While Connection.Exists("ADSAccount", f.AndRelation(f.Comparison("SAMAccountName", Acc, ValType.String, CompareOperator.Equal), _
					f.UidComparison("UID_ADSDomain", Domain), _
					f.UidComparison("UID_ADSAccount", $UID_ADSAccount$, CompareOperator.NotEqual)))
					i = i + 1
					Acc = txt.ToString() & i.ToString()
				Loop
		End Select

	End If
	
	Value = Acc

End If

    'UID_ADSAccount

'$FK(UID_Person).UID_Department$
'$FK(UID_Person).UID_Locality$
'$FK(UID_Person).UID_ProfitCenter$
#If ORG Then
'$FK(UID_Person).UID_Org$
#End If
If (Not CBool(Variables("FULLSYNC")) Or _
(CBool(Variables("FULLSYNC")) And ($FK(UID_Person).ImportSource$).StartsWith("SF_HR"))) Then
Select Case ($FK(UID_TSBBehavior).ITDataUsage:Int$)
	Case 0:'do not get data from employee
	Case -1:'fill property initially from the ITData of the employee
		If Not $[IsLoaded]:Bool$ Then
			Value = TSB_ITDataFromOrg($UID_Person$, $UID_TSBAccountDef$, Base.TableDef.Columns("UID_ADSContainer")) 
		End If
	Case 1:'update property depending on ITData of the employee
		Value = TSB_ITDataFromOrg($UID_Person$, $UID_TSBAccountDef$, Base.TableDef.Columns("UID_ADSContainer")) 
End Select
End If

    'UID_ADSDomain

If Not $[IsLoaded]:Bool$ AndAlso ((Not CBool(Variables("FULLSYNC")) Or _
(CBool(Variables("FULLSYNC")) And ($FK(UID_Person).ImportSource$).StartsWith("SF_HR")))) Then
	If $UID_ADSContainer$ <> "" Then  
		Value = $FK(UID_ADSContainer).UID_ADSDomain$
	ElseIf $UID_TSBAccountDef$ <> "" Then
		Value = $FK(UID_TSBAccountDef).FK(ObjectKeyTargetSystem),ADSDomain.UID_ADSDomain$
	End If
End If

    'UserPassword

'If Not CBool(Variables("FULLSYNC")) Then
	' Check if the password reset is not triggered through the D1IM capture agent.
'	If Not CStr(Connection.Variables.Get("CaptureAgent")) = $UID_ADSAccount$ Then 
'		If Not $[IsLoaded]:Bool$ Then 
'			If Connection.GetConfigParm("TargetSystem\ADS\Accounts\NotRequirePassword")<>"1" Then 
			'Set password if configparm "TargetSystem\ADS\Accounts\NotRequirePassword" not set on adding
'				Value = VI_GetPassword ("ADS", $FK(UID_Person).CentralPassword$, True) 
'			End If 
'		ElseIf Not CBool(Connection.Variables.Get("ORGITDATA")) Then 
			' Do not update the password for privileged accounts
'			If Not $IsPrivilegedAccount:Bool$ Then 
'				Dim tmp As String = VI_GetPassword ("ADS", $FK(UID_Person).CentralPassword$, False) 
			
'				If Len(tmp) > 0 Then 
'					Value = tmp 
'				End If 
'			End If
'		End If 
'	End If
'End If
If (Not CBool(Variables("FULLSYNC")) Or _
(CBool(Variables("FULLSYNC")) And ($FK(UID_Person).ImportSource$).StartsWith("SF_HR"))) Then
	If Not $[IsLoaded]:Bool$ Then
			If $IsPrivilegedAccount:Bool$ Then
				Value = TSB_GetPassword(entity, "UserPassword", "ADS", $FK(UID_Person).CentralPassword$, True)
			Else 
				If $FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = "AD-User-Conta-S" Then
					Value = CCC_TPL_PasswordContaS(Replace($FK(UID_Person).CentralAccount$, "S", ""), $FK(UID_Person).FirstName$, $FK(UID_Person).LastName$, entity)
				Else
					Value = CCC_TPL_UserPassword($FK(UID_Person).CustomProperty02$, $FK(UID_Person).FirstName$, $FK(UID_Person).LastName$, entity)
				End If
			End if
	End If
End If

    'UserPrincipalName

'If Not CBool(Variables("FULLSYNC")) Then
'	Select Case ($FK(UID_TSBBehavior).ITDataUsage:Int$)
'		Case 1:'update property depending on ITData of the employee
'			If $UID_ADSDomain$ <> "" AndAlso $SAMAccountName$ <> "" Then
'	  			Value = $SAMAccountName$ & "@" & $FK(UID_ADSDomain).ADSDomainName$
'			End If
'		Case Else:'fill property initially
'			If Not $[IsLoaded]:Bool$ Then
'				If $UID_ADSDomain$ <> "" AndAlso $SAMAccountName$ <> "" Then
'	  				Value = $SAMAccountName$ & "@" & $FK(UID_ADSDomain).ADSDomainName$
'				End If
'			End If
'	End Select
'End If

If (Not CBool(Variables("FULLSYNC")) Or _
(CBool(Variables("FULLSYNC")) And ($FK(UID_Person).ImportSource$).StartsWith("SF_HR"))) Then
		Select Case ($FK(UID_TSBBehavior).ITDataUsage:Int$)
			Case 0:'Do not change UPN, it's not managed
			
			Case 1:'update property depending on ITData of the employee
				If $FK(UID_Person).IsInActive:Bool$ = False Then
					If Not $IsPrivilegedAccount:Bool$ then
						if $FK(UID_Person).DefaultEMailAddress$ <> "" Then	
							Value = CCC_Format_UPN($FK(UID_Person).DefaultEMailAddress$)
						elseif $UID_ADSDomain$ <> "" AndAlso $SAMAccountName$ <> "" Then
							Value = $SAMAccountName$ & "@" & $FK(UID_ADSDomain).ADSDomainName$
						end if
					elseif $UID_ADSDomain$ <> "" AndAlso $SAMAccountName$ <> "" Then
						Value = $SAMAccountName$ & "@" & $FK(UID_ADSDomain).ADSDomainName$
					end if
				'Else 
					'Value = $FK(UID_Person).CentralAccount$ & "@" & $FK(UID_ADSDomain).ADSDomainName$
				End If
					
			Case -1:'fill property initially
				If Not $[IsLoaded]:Bool$ Then
					if Not $IsPrivilegedAccount:Bool$ then
						if $FK(UID_Person).DefaultEMailAddress$ <> "" Then
	 						Value = CCC_Format_UPN($FK(UID_Person).DefaultEMailAddress$)
						elseif $UID_ADSDomain$ <> "" AndAlso $SAMAccountName$ <> "" Then
							Value = $SAMAccountName$ & "@" & $FK(UID_ADSDomain).ADSDomainName$
						end if
					elseif $UID_ADSDomain$ <> "" AndAlso $SAMAccountName$ <> "" Then
						Value = $SAMAccountName$ & "@" & $FK(UID_ADSDomain).ADSDomainName$
					end if
				End If
		End Select
End If