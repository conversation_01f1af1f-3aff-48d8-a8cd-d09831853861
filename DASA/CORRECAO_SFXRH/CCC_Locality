' Verifica se o campo CCC_Locality não está vazio
If Not String.IsNullOrEmpty($CCC_Locality$) Then
    ' Declaração de variável para armazenar o UID_Locality
    Dim uidLoc As String = CStr(Connection.GetSingleProperty("Locality", "UID_Locality", "Ident_Locality = '" & $CCC_Locality$ & "'"))

    ' Verifica se encontrou o UID_Locality e preenche o campo
    If Not String.IsNullOrEmpty(uidLoc) Then
        ' Atualiza o campo UID_Locality com o valor encontrado
        Entity.PutValue("UID_Locality", uidLoc)
    Else
        ' Se não encontrou, define UID_Locality como vazio
        Entity.PutValue("UID_Locality", Nothing)
    End If
End If
