' Verifica se o campo CCC_PersonHead não está vazio
If Not String.IsNullOrEmpty($CCC_PersonHead$) Then
    ' Declara uma variável para armazenar o UID_PersonHead
    Dim uidPersonHead As String = CStr(Connection.GetSingleProperty("Person", "UID_Person", "PersonnelNumber = '" & $CCC_PersonHead$ & "'"))

    ' Verifica se encontrou o UID_PersonHead e preenche o campo
    If Not String.IsNullOrEmpty(uidPersonHead) Then
        ' Atualiza o campo UID_PersonHead com o valor encontrado
        Entity.PutValue("UID_PersonHead", uidPersonHead)
    Else
        ' Se não encontrou, define UID_PersonHead como vazio
        Entity.PutValue("UID_PersonHead", Nothing)
    End If
End If


' Verifica se o campo CCC_PersonHead não está vazio
If Not String.IsNullOrEmpty($CCC_PersonHead$) Then
    ' Declara uma variável para armazenar o UID_PersonHead
    Dim uidPersonHead As String = CStr(Connection.GetSingleProperty("Person", "UID_Person", "PersonnelNumber = '" & $CCC_PersonHead$ & "'"))

    ' Verifica se encontrou o UID_PersonHead e preenche o campo
    If Not String.IsNullOrEmpty(uidPersonHead) Then
        ' Atualiza o campo UID_PersonHead com o valor encontrado
        Entity.PutValue("UID_PersonHead", uidPersonHead)
        ' Mensagem de depuração confirmando o preenchimento
        DialogJournal.WriteLine("Sucesso: UID_PersonHead preenchido com " & uidPersonHead & " para PersonnelNumber " & $CCC_PersonHead$)
    Else
        ' Define UID_PersonHead como vazio e registra uma mensagem de erro
        Entity.PutValue("UID_PersonHead", "")
        DialogJournal.WriteLine("Erro: Nenhum UID_Person encontrado para o PersonnelNumber " & $CCC_PersonHead$)
    End If
Else
    ' Mensagem de depuração se o campo CCC_PersonHead estiver vazio
    DialogJournal.WriteLine("Aviso: O campo CCC_PersonHead está vazio; UID_PersonHead não foi preenchido.")
End If

' Verifica se o campo CCC_PersonHead não está vazio
If Not String.IsNullOrEmpty($CCC_PersonHead$) Then
    ' Declara uma variável para armazenar o UID_PersonHead
    Dim uidPersonHead As String = CStr(Connection.GetSingleProperty("Person", "UID_Person", "PersonnelNumber = '" & $CCC_PersonHead$ & "'"))

    ' Verifica se encontrou o UID_PersonHead e preenche o campo, desde que não seja uma modificação de chave primária
    If Not String.IsNullOrEmpty(uidPersonHead) Then
        ' Atualiza o campo UID_PersonHead com o valor encontrado, verificando se não está alterando uma chave primária
        If Entity.Columns("UID_PersonHead").IsReadOnly = False Then
            Entity.PutValue("UID_PersonHead", uidPersonHead)
        Else
            ' Registrar um aviso caso a coluna seja de somente leitura
            DialogJournal.WriteLine("Aviso: Tentativa de modificar um campo de somente leitura: UID_PersonHead.")
        End If
    Else
        ' Se não encontrou, define UID_PersonHead como vazio, desde que não seja uma modificação de chave primária
        If Entity.Columns("UID_PersonHead").IsReadOnly = False Then
            Entity.PutValue("UID_PersonHead", "")
        Else
            DialogJournal.WriteLine("Aviso: Tentativa de limpar um campo de somente leitura: UID_PersonHead.")
        End If
    End If
End If



