' Verifica se o campo CCC_DepartamentSF não está vazio
If Not String.IsNullOrEmpty($CCC_DepartamentSF$) Then
    ' Declaração de variável para armazenar o UID_Department
    Dim uidDepartment As String = CStr(Connection.GetSingleProperty("Department", "UID_Department", "ShortName = '" & $CCC_DepartamentSF$ & "'"))

    ' Verifica se encontrou o UID_Department e preenche o campo
    If Not String.IsNullOrEmpty(uidDepartment) Then
        ' Atualiza o campo UID_Department com o valor encontrado
        Entity.PutValue("UID_Department", uidDepartment)
    Else
        ' Se não encontrou, define UID_Department como vazio
        Entity.PutValue("UID_Department", Nothing)
    End If
End If
