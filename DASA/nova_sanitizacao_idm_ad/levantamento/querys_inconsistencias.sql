--- Contas inativas em OUs erradas

SELECT 
    ads.*, 
    p.*
FROM 
    adsaccount ads
JOIN 
    person p ON p.uid_person = ads.uid_person
WHERE
    ads.accountdisabled = 1 AND
    ads.uid_adscontainer NOT IN (
        '4aba61e5-a4fa-4b7e-838c-d9b454ba9808', -- terceiros
        '********-7ae8-4557-98a8-4bfd53cd1b49', -- conta a
        '120fdac2-4d3a-45e4-84f3-6e86ba4589a9', -- conta s
        'c0376883-260b-4958-84a9-cbe448095754'  -- internos
    );

--- OUs das contas em OUs incorretas

SELECT DISTINCT
    c.uid_adscontainer,
    c.C<PERSON>,
    c.distinguishedname
FROM 
    adsaccount ads
JOIN 
    adscontainer c ON c.uid_adscontainer = ads.uid_adscontainer
WHERE
    ads.accountdisabled = 1 AND
    ads.uid_adscontainer NOT IN (
        '4aba61e5-a4fa-4b7e-838c-d9b454ba9808', -- terceiros
        '********-7ae8-4557-98a8-4bfd53cd1b49', -- conta a
        '120fdac2-4d3a-45e4-84f3-6e86ba4589a9', -- conta s
        'c0376883-260b-4958-84a9-cbe448095754'  -- internos
    );

--- Identidades ativas com mais de uma conta ativa no AD

SELECT 
    p.uid_person,
    p.preferredname AS person_name,
    COUNT(ads.uid_adsaccount) AS active_ad_accounts
FROM 
    person p
JOIN 
    adsaccount ads ON p.uid_person = ads.uid_person
WHERE 
    p.isinactive = 0 AND  -- Pessoa ativa
    ads.accountdisabled = 0  -- Conta AD ativa
GROUP BY 
    p.uid_person, p.preferredname
HAVING 
    COUNT(ads.uid_adsaccount) > 1;


--- Bruto - Identidades ativas com mais de uma conta ativa no AD

SELECT 
    p.uid_person,
    p.preferredname AS person_name,
    ads.uid_adsaccount,
    ads.displayname AS ad_account_name,
    ads.samaccountname,
    ads.accountdisabled
FROM 
    person p
JOIN 
    adsaccount ads ON p.uid_person = ads.uid_person
WHERE 
    p.isinactive = 0 AND  -- Pessoa ativa
    ads.accountdisabled = 0 AND  -- Conta AD ativa
    p.uid_person IN (
        SELECT 
            p_sub.uid_person
        FROM 
            person p_sub
        JOIN 
            adsaccount ads_sub ON p_sub.uid_person = ads_sub.uid_person
        WHERE 
            p_sub.isinactive = 0 AND
            ads_sub.accountdisabled = 0
        GROUP BY 
            p_sub.uid_person
        HAVING 
            COUNT(ads_sub.uid_adsaccount) > 1
    );

--- Identidades inativas com contas ativas no AD

SELECT 
    p.uid_person,
    p.preferredname AS person_name,
    ads.uid_adsaccount,
    ads.displayname AS ad_account_name,
    ads.samaccountname,
    ads.accountdisabled
FROM 
    person p
JOIN 
    adsaccount ads ON p.uid_person = ads.uid_person
WHERE 
    p.isinactive = 1 AND  -- Pessoa inativa
    ads.accountdisabled = 0;  -- Conta AD ativa

--- Bruto - Identidades inativas com contas ativas no AD

SELECT 
    p.uid_person,
    p.preferredname AS person_name,
    ads.uid_adsaccount,
    ads.displayname AS ad_account_name,
    ads.samaccountname,
    ads.accountdisabled
FROM 
    person p
JOIN 
    adsaccount ads ON p.uid_person = ads.uid_person
WHERE 
    p.isinactive = 1 AND  -- Pessoa inativa
    ads.accountdisabled = 0;  -- Conta AD ativa
