Date: Wed, 30 Jul 2025 21:01:52 +0000 (UTC)
Message-ID: <718017742.1.1753909312450@02c588a86f4b>
Subject: Exported From Confluence
MIME-Version: 1.0
Content-Type: multipart/related; 
	boundary="----=_Part_0_1601909373.1753909312406"

------=_Part_0_1601909373.1753909312406
Content-Type: text/html; charset=UTF-8
Content-Transfer-Encoding: quoted-printable
Content-Location: file:///C:/exported.html

<html xmlns:o=3D'urn:schemas-microsoft-com:office:office'
      xmlns:w=3D'urn:schemas-microsoft-com:office:word'
      xmlns:v=3D'urn:schemas-microsoft-com:vml'
      xmlns=3D'urn:w3-org-ns:HTML'>
<head>
    <meta http-equiv=3D"Content-Type" content=3D"text/html; charset=3Dutf-8=
">
    <title>(DAS001G) - S#RITM0874589 | R#17086 - Plano de Roll Out e Roll B=
ack</title>
    <!--[if gte mso 9]>
    <xml>
        <o:OfficeDocumentSettings>
            <o:TargetScreenSize>1024x640</o:TargetScreenSize>
            <o:PixelsPerInch>72</o:PixelsPerInch>
            <o:AllowPNG/>
        </o:OfficeDocumentSettings>
        <w:WordDocument>
            <w:View>Print</w:View>
            <w:Zoom>90</w:Zoom>
            <w:DoNotOptimizeForBrowser/>
        </w:WordDocument>
    </xml>
    <![endif]-->
    <style>
                <!--
        @page Section1 {
            size: 8.5in 11.0in;
            margin: 1.0in;
            mso-header-margin: .5in;
            mso-footer-margin: .5in;
            mso-paper-source: 0;
        }

        table {
            border: solid 1px;
            border-collapse: collapse;
        }

        table td, table th {
            border: solid 1px;
            padding: 5px;
        }

        td {
            page-break-inside: avoid;
        }

        tr {
            page-break-after: avoid;
        }

        div.Section1 {
            page: Section1;
        }

        /* Confluence print stylesheet. Common to all themes for print medi=
a */
/* Full of !important until we improve batching for print CSS */

@media print {
    #main {
        padding-bottom: 1em !important; /* The default padding of 6em is to=
o much for printouts */
    }

    body {
        font: var(--ds-font-body-small, Arial, Helvetica, FreeSans, sans-se=
rif);
    }

    body, #full-height-container, #main, #page, #content, .has-personal-sid=
ebar #content {
        background: var(--ds-surface, #fff) !important;
        color: var(--ds-text, #000) !important;
        border: 0 !important;
        width: 100% !important;
        height: auto !important;
        min-height: auto !important;
        margin: 0 !important;
        padding: 0 !important;
        display: block !important;
    }

    a, a:link, a:visited, a:focus, a:hover, a:active {
        color: var(--ds-text, #000);
    }

    #content h1,
    #content h2,
    #content h3,
    #content h4,
    #content h5,
    #content h6 {
        page-break-after: avoid;
    }

    pre {
        font: var(--ds-font-code, Monaco, "Courier New", monospace);
    }

    #header,
    .aui-header-inner,
    #navigation,
    #sidebar,
    .sidebar,
    #personal-info-sidebar,
    .ia-fixed-sidebar,
    .page-actions,
    .navmenu,
    .ajs-menu-bar,
    .noprint,
    .inline-control-link,
    .inline-control-link a,
    a.show-labels-editor,
    .global-comment-actions,
    .comment-actions,
    .quick-comment-container,
    #addcomment {
        display: none !important;
    }

    /* CONF-28544 cannot print multiple pages in IE */
    #splitter-content {
        position: relative !important;
    }

    .comment .date::before {
        content: none !important; /* remove middot for print view */
    }

    h1.pagetitle img {
        height: auto;
        width: auto;
    }

    .print-only {
        display: block;
    }

    #footer {
        position: relative !important; /* CONF-17506 Place the footer at en=
d of the content */
        margin: 0;
        padding: 0;
        background: none;
        clear: both;
    }

    #poweredby {
        border-top: none;
        background: none;
    }

    #poweredby li.print-only {
        display: list-item;
        font-style: italic;
    }

    #poweredby li.noprint {
        display: none;
    }

    /* no width controls in print */
    .wiki-content .table-wrap,
    .wiki-content p,
    .panel .codeContent,
    .panel .codeContent pre,
    .image-wrap {
        overflow: visible !important;
    }

    /* TODO - should this work? */
    #children-section,
    #comments-section .comment,
    #comments-section .comment .comment-body,
    #comments-section .comment .comment-content,
    #comments-section .comment p {
        page-break-inside: avoid;
    }

    #page-children a {
        text-decoration: none;
    }

    /**
     hide twixies

     the specificity here is a hack because print styles
     are getting loaded before the base styles. */
    #comments-section.pageSection .section-header,
    #comments-section.pageSection .section-title,
    #children-section.pageSection .section-header,
    #children-section.pageSection .section-title,
    .children-show-hide {
        padding-left: 0;
        margin-left: 0;
    }

    .children-show-hide.icon {
        display: none;
    }

    /* personal sidebar */
    .has-personal-sidebar #content {
        margin-right: 0px;
    }

    .has-personal-sidebar #content .pageSection {
        margin-right: 0px;
    }

    .no-print, .no-print * {
        display: none !important;
    }
}
-->
    </style>
</head>
<body>
    <h1>(DAS001G) - S#RITM0874589 | R#17086 - Plano de Roll Out e Roll Back=
</h1>
    <div class=3D"Section1">
        <p></p><span class=3D"confluence-embedded-file-wrapper image-center=
-wrapper confluence-embedded-manual-size"><img class=3D"confluence-embedded=
-image image-center" alt=3D"image-20250407-190859.png" width=3D"468" loadin=
g=3D"lazy" src=3D"22811be4d0f6030c42417f736d0b02d9eb1b32174e217bb58eb2f85d4=
ba6fd8c" data-image-src=3D"https://sec4u.atlassian.net/wiki/download/attach=
ments/33259545/image-20250407-190859.png?version=3D1&amp;modificationDate=
=3D1745602233847&amp;cacheVersion=3D1&amp;api=3Dv2" data-height=3D"912" dat=
a-width=3D"1916" data-unresolved-comment-count=3D"0" data-linked-resource-i=
d=3D"302580016" data-linked-resource-version=3D"1" data-linked-resource-typ=
e=3D"attachment" data-linked-resource-default-alias=3D"image-20250407-19085=
9.png" data-base-url=3D"https://sec4u.atlassian.net/wiki" data-linked-resou=
rce-content-type=3D"image/png" data-linked-resource-container-id=3D"3325954=
5" data-linked-resource-container-version=3D"64" data-media-id=3D"71a91d25-=
2dc5-4354-a889-5c9186965eeb" data-media-type=3D"file" height=3D"222"></span=
>
<p></p>
<p></p>
<div class=3D"contentLayout2">
<div class=3D"columnLayout two-equal" data-layout=3D"two-equal">
<div class=3D"cell normal" data-type=3D"normal">
<div class=3D"innerCell">
<div class=3D"columnMacro" style=3D"width:80%;min-width:80%;max-width:80%;"=
>
<p>Todos os direitos reservados =C3=A0 Sec4U. Sendo proibida a reprodu=C3=
=A7=C3=A3o, mesmo que parcial, por qualquer processo, seja mec=C3=A2nico, e=
letr=C3=B4nico, grava=C3=A7=C3=A3o, digitaliza=C3=A7=C3=A3o ou outros, exis=
tentes ou que venham a ser criados, sem pr=C3=A9via autoriza=C3=A7=C3=A3o e=
scrita da Sec4U. Ressalvo desse t=C3=B3pico a utiliza=C3=A7=C3=A3o do docum=
ento pelo cliente ao qual se refere.</p>
<p></p>
<p>Quaisquer marcas de produtos ou servi=C3=A7os usados neste documento s=
=C3=A3o marcas registradas das respectivas empresas, as quais s=C3=A3o util=
izadas ao longo deste documento apenas de forma editorial e para o benef=C3=
=ADcio de tais empresas.</p>
<p></p>
<p>Se voc=C3=AA recebeu este documento em erro, por favor avise-me imediata=
mente por telefone ou e-mail e apague esta mensagem de seus sistemas.</p>
<p></p>
</div>
</div>
</div>
<div class=3D"cell normal" data-type=3D"normal">
<div class=3D"innerCell">
<div class=3D"sectionMacro">
<div class=3D"sectionMacroRow">
<p></p>
<p></p>
</div>
</div>
</div>
</div>
</div>
</div>
<div class=3D"contentLayout2">
<div class=3D"columnLayout two-right-sidebar" data-layout=3D"two-right-side=
bar">
<div class=3D"cell normal" data-type=3D"normal">
<div class=3D"innerCell">
<div class=3D"columnMacro" style=3D"width:35%;min-width:35%;max-width:35%;"=
>
<span class=3D"confluence-embedded-file-wrapper image-left-wrapper confluen=
ce-embedded-manual-size"><img class=3D"confluence-embedded-image image-left=
" alt=3D"image-20250407-172530.png" width=3D"201" loading=3D"lazy" src=3D"0=
2aebe1817d106b93fea3cd4cb77744900386d0dde585df0994951ac75fa8da2" data-image=
-src=3D"https://sec4u.atlassian.net/wiki/download/attachments/33259551/imag=
e-20250407-172530.png?version=3D1&amp;modificationDate=3D1745602274581&amp;=
cacheVersion=3D1&amp;api=3Dv2" data-height=3D"582" data-width=3D"1200" data=
-unresolved-comment-count=3D"0" data-linked-resource-id=3D"302547193" data-=
linked-resource-version=3D"1" data-linked-resource-type=3D"attachment" data=
-linked-resource-default-alias=3D"image-20250407-172530.png" data-base-url=
=3D"https://sec4u.atlassian.net/wiki" data-linked-resource-content-type=3D"=
image/png" data-linked-resource-container-id=3D"33259551" data-linked-resou=
rce-container-version=3D"48" data-media-id=3D"df288621-4f13-49e6-b399-94935=
dc3d053" data-media-type=3D"file" height=3D"97"></span>
<p>Rua Dr. Rafael de Barros, 209 =E2=80=93 12=C2=BA Andar<br>
S=C3=A3o Paulo =E2=80=93 SP<br>
Telefone: (11) 3522-4849<br><a href=3D"https://www.sec4u.com.br/" class=3D"=
external-link" rel=3D"nofollow">www.sec4u.com.br</a></p>
</div>
</div>
</div>
<div class=3D"cell aside" data-type=3D"aside">
<div class=3D"innerCell">
<p>&nbsp;</p>
</div>
</div>
</div>
<div class=3D"columnLayout fixed-width" data-layout=3D"fixed-width">
<div class=3D"cell normal" data-type=3D"normal">
<div class=3D"innerCell">
<p></p>
</div>
</div>
</div>
</div><span class=3D"confluence-embedded-file-wrapper image-center-wrapper =
confluence-embedded-manual-size"><img class=3D"confluence-embedded-image im=
age-center" width=3D"468" loading=3D"lazy" src=3D"b312a30ffd65deef7621b1777=
f5f4cd413073e0a99e9b09ff61707ee9276d0d6" data-image-src=3D"https://sec4u.at=
lassian.net/wiki/download/attachments/401997914/Dasa.png?version=3D1&amp;mo=
dificationDate=3D1753103364609&amp;cacheVersion=3D1&amp;api=3Dv2" data-heig=
ht=3D"117" data-width=3D"431" data-unresolved-comment-count=3D"0" data-link=
ed-resource-id=3D"401408299" data-linked-resource-version=3D"1" data-linked=
-resource-type=3D"attachment" data-linked-resource-default-alias=3D"Dasa.pn=
g" data-base-url=3D"https://sec4u.atlassian.net/wiki" data-linked-resource-=
content-type=3D"image/png" data-linked-resource-container-id=3D"401997914" =
data-linked-resource-container-version=3D"7" data-media-id=3D"baf86b70-8679=
-473a-b17c-5adc621d8d69" data-media-type=3D"file" height=3D"127"></span>
<h1 id=3D"id-(DAS001G)-S#RITM0874589|R#17086-PlanodeRollOuteRollBack-(DAS00=
1G)-S#RITM0874589|R#17086-PlanodeRollOuteRollBack-Mapeamentodonomesocialemc=
onectorSuccessFactorsHR">(DAS001G) - S#RITM0874589 | R#17086 - Plano de Rol=
l Out e Roll Back - Mapeamento do nome social em conector SuccessFactors HR=
</h1>
<div class=3D"table-wrap">
<table data-table-width=3D"1800" data-layout=3D"center" data-local-id=3D"1a=
486f2b-aec9-4ebc-9a07-869aa92eb4aa" class=3D"confluenceTable">
<tbody>
<tr>
<th colspan=3D"6" class=3D"confluenceTh">
<p></p></th>
</tr>
<tr>
<td data-highlight-colour=3D"#f0f0f0" class=3D"confluenceTd">
<p><strong>*Respons=C3=A1vel SEC4U</strong></p></td>
<td class=3D"confluenceTd">
<p>Pedro Ribeiro<br>
(11) 991807936<br><a href=3D"mailto:<EMAIL>" class=
=3D"external-link" rel=3D"nofollow"><EMAIL></a></p><=
/td>
<td data-highlight-colour=3D"#f0f0f0" class=3D"confluenceTd">
<p><strong>*Respons=C3=A1vel (Cliente):</strong></p></td>
<td colspan=3D"3" class=3D"confluenceTd">
<p>Marcio Zafalao<br><a href=3D"mailto:<EMAIL>" c=
lass=3D"external-link" rel=3D"nofollow"><EMAIL></=
a></p></td>
</tr>
<tr>
<td data-highlight-colour=3D"#f0f0f0" class=3D"confluenceTd">
<p><strong>*Data da execu=C3=A7=C3=A3o:</strong></p></td>
<td class=3D"confluenceTd">
<p><time datetime=3D"2025-07-31" class=3D"date-upcoming">31 de jul. de 2025=
</time></p></td>
<td data-highlight-colour=3D"#f0f0f0" class=3D"confluenceTd">
<p><strong>*Hor=C3=A1rio da execu=C3=A7=C3=A3o:</strong></p></td>
<td class=3D"confluenceTd">
<p>18:00</p></td>
<td data-highlight-colour=3D"#f0f0f0" rowspan=3D"3" class=3D"confluenceTd">
<p></p>
<p></p>
<p><strong>*Tempo total:</strong></p></td>
<td rowspan=3D"3" class=3D"confluenceTd">
<p></p>
<p></p>
<p>01:00</p></td>
</tr>
<tr>
<td data-highlight-colour=3D"#f0f0f0" class=3D"confluenceTd">
<p><strong>*Data limite:</strong></p></td>
<td class=3D"confluenceTd">
<p><time datetime=3D"2025-07-31" class=3D"date-upcoming">31 de jul. de 2025=
</time></p></td>
<td class=3D"confluenceTd">
<p><strong>*Hor=C3=A1rio limite:</strong></p></td>
<td class=3D"confluenceTd">
<p>19:00</p></td>
</tr>
<tr>
<td data-highlight-colour=3D"#f0f0f0" class=3D"confluenceTd">
<p><strong>*Indisponibilidade:</strong></p></td>
<td class=3D"confluenceTd">
<ul class=3D"inline-task-list" data-inline-tasks-content-id=3D"401997914">
<li data-inline-task-id=3D"90"><span class=3D"placeholder-inline-tasks">Sim=
</span></li>
<li class=3D"checked" data-inline-task-id=3D"91"><span class=3D"placeholder=
-inline-tasks">N=C3=A3o</span></li>
</ul></td>
<td data-highlight-colour=3D"#f0f0f0" class=3D"confluenceTd">
<p><strong>*Tempo de Indisponibilidade:</strong></p></td>
<td class=3D"confluenceTd">
<p>00:00</p></td>
</tr>
<tr>
<td data-highlight-colour=3D"#f0f0f0" class=3D"confluenceTd">
<p><strong>*Objetivo:</strong></p></td>
<td colspan=3D"5" class=3D"confluenceTd">
<p>Escrever, com uma vis=C3=A3o t=C3=A9cnica, breve e objetiva, o que preci=
sa ser feito.</p></td>
</tr>
<tr>
<td data-highlight-colour=3D"#f0f0f0" class=3D"confluenceTd">
<p><strong>*Pr=C3=A9-requisitos:</strong></p></td>
<td data-highlight-colour=3D"#f0f0f0" colspan=3D"5" class=3D"confluenceTd">
<ul class=3D"inline-task-list" data-inline-tasks-content-id=3D"401997914">
<li class=3D"checked" data-inline-task-id=3D"92"><span class=3D"placeholder=
-inline-tasks">Mapeamento do campo de nome social no sistema.</span></li>
</ul></td>
</tr>
<tr>
<td colspan=3D"6" class=3D"confluenceTd">
<div class=3D"table-wrap">
<table data-layout=3D"default" data-local-id=3D"c8330b8e-146d-4eaf-a453-e67=
553659f91" class=3D"confluenceTable">
<colgroup>
<col style=3D"width: 148.0px;">
<col style=3D"width: 73.0px;">
<col style=3D"width: 223.0px;">
<col style=3D"width: 207.0px;">
<col style=3D"width: 145.0px;">
<col style=3D"width: 114.0px;">
<col style=3D"width: 126.0px;">
<col style=3D"width: 148.0px;">
</colgroup>
<tbody>
<tr>
<td colspan=3D"8" class=3D"confluenceTd">
<p style=3D"text-align: right;"><strong>*TEMPO ESTIMADO: </strong>00:30</p>=
</td>
</tr>
<tr>
<td data-highlight-colour=3D"#ddfade" rowspan=3D"2" class=3D"confluenceTd">
<p><strong>Plano de</strong><br><strong>Execu=C3=A7=C3=A3o</strong></p></td=
>
<td class=3D"confluenceTd">
<p><strong>*Item</strong></p></td>
<td class=3D"confluenceTd">
<p><strong>*Atividade</strong></p></td>
<td class=3D"confluenceTd">
<p><strong>*Descri=C3=A7=C3=A3o</strong></p></td>
<td class=3D"confluenceTd">
<p><strong>*Respons=C3=A1vel</strong></p></td>
<td class=3D"confluenceTd">
<p><strong>*Hora in=C3=ADcio</strong></p></td>
<td class=3D"confluenceTd">
<p><strong>*Hora final</strong></p></td>
<td class=3D"confluenceTd">
<p><strong>Observa=C3=A7=C3=A3o</strong></p></td>
</tr>
<tr>
<td class=3D"confluenceTd">
<p>1</p></td>
<td class=3D"confluenceTd">
<p>Cria=C3=A7=C3=A3o do campo customizado no Starling</p></td>
<td class=3D"confluenceTd">
<p>No Starling, no conector de produ=C3=A7=C3=A3o do sistema <em>SuccessFac=
tors HR</em>, colocar em modo de edi=C3=A7=C3=A3o e inserir as credenciais =
necess=C3=A1rias.</p>
<p>Ap=C3=B3s autenticado, no esquema <em>Employees</em>, criar um campo cus=
tomizado inserindo o caminho para o campo de nome social, e inserir uma des=
cri=C3=A7=C3=A3o adequada para o campo.</p>
<p>Realizar um teste de conex=C3=A3o para garantir que o campo foi salvo e =
inclu=C3=ADdo no esquema.</p></td>
<td class=3D"confluenceTd">
<p>Pedro Ribeiro</p></td>
<td class=3D"confluenceTd">
<p>18:00</p></td>
<td class=3D"confluenceTd">
<p>18:15</p></td>
<td class=3D"confluenceTd">
<p></p></td>
</tr>
<tr>
<td data-highlight-colour=3D"#ddfade" class=3D"confluenceTd">
<p></p></td>
<td class=3D"confluenceTd">
<p>2</p></td>
<td class=3D"confluenceTd">
<p>Mapeamento do novo campo no IDM</p></td>
<td class=3D"confluenceTd">
<p>No m=C3=B3dulo <em>Synchronization Editor</em>, realizar a atualiza=C3=
=A7=C3=A3o do esquema para o conector do <em>SuccessFactors HR</em>, e conf=
erindo logo ap=C3=B3s se o campo novo foi adicionado para o mapeamento do e=
squema <em>Employees</em>.</p>
<p>Trocar o campo associado para o campo <em>PreferredName</em> no IDM para=
 o novo campo customizado de nome social.</p></td>
<td class=3D"confluenceTd">
<p>Pedro Ribeiro</p></td>
<td class=3D"confluenceTd">
<p>18:15</p></td>
<td class=3D"confluenceTd">
<p>18:30</p></td>
<td class=3D"confluenceTd">
<p></p></td>
</tr>
</tbody>
</table>
</div></td>
</tr>
<tr>
<td colspan=3D"6" class=3D"confluenceTd">
<div class=3D"table-wrap">
<table data-layout=3D"default" data-local-id=3D"e758380e-8a02-4372-ba0c-1c4=
475a6039c" class=3D"confluenceTable">
<colgroup>
<col style=3D"width: 148.0px;">
<col style=3D"width: 93.0px;">
<col style=3D"width: 149.0px;">
<col style=3D"width: 237.0px;">
<col style=3D"width: 140.0px;">
<col style=3D"width: 148.0px;">
<col style=3D"width: 131.0px;">
<col style=3D"width: 138.0px;">
</colgroup>
<tbody>
<tr>
<td colspan=3D"8" class=3D"confluenceTd">
<p style=3D"text-align: right;"><strong>*TEMPO ESTIMADO: </strong>00:00</p>=
</td>
</tr>
<tr>
<td data-highlight-colour=3D"#f0f0f0" rowspan=3D"2" class=3D"confluenceTd">
<p><strong>Plano de</strong><br><strong>Valida=C3=A7=C3=A3o</strong></p></t=
d>
<td class=3D"confluenceTd">
<p><strong>*Item</strong></p></td>
<td class=3D"confluenceTd">
<p><strong>*Atividade</strong></p></td>
<td class=3D"confluenceTd">
<p><strong>*Descri=C3=A7=C3=A3o</strong></p></td>
<td class=3D"confluenceTd">
<p><strong>*Respons=C3=A1vel</strong></p></td>
<td class=3D"confluenceTd">
<p><strong>*Hora in=C3=ADcio</strong></p></td>
<td class=3D"confluenceTd">
<p><strong>*Hora final</strong></p></td>
<td class=3D"confluenceTd">
<p><strong>Observa=C3=A7=C3=A3o</strong></p></td>
</tr>
<tr>
<td class=3D"confluenceTd">
<p>1</p></td>
<td class=3D"confluenceTd">
<p>Acompanhar agrega=C3=A7=C3=A3o de colaboradores ativos</p></td>
<td class=3D"confluenceTd">
<p>Para valida=C3=A7=C3=A3o, acompanhar as agrega=C3=A7=C3=B5es que realiza=
m a atualiza=C3=A7=C3=A3o de dados dos colaboradores ativos no IDM, avalian=
do especificamente o caso passado com o problema de mapeamento do nome soci=
al.</p></td>
<td class=3D"confluenceTd">
<p>Pedro Ribeiro</p></td>
<td class=3D"confluenceTd">
<p>N/A</p></td>
<td class=3D"confluenceTd">
<p>N/A</p></td>
<td class=3D"confluenceTd">
<p></p></td>
</tr>
</tbody>
</table>
</div></td>
</tr>
<tr>
<td colspan=3D"6" class=3D"confluenceTd">
<div class=3D"table-wrap">
<table data-layout=3D"default" data-local-id=3D"ecf58873-94b8-4e73-95d8-1ed=
1b4a5243a" class=3D"confluenceTable">
<colgroup>
<col style=3D"width: 148.0px;">
<col style=3D"width: 89.0px;">
<col style=3D"width: 173.0px;">
<col style=3D"width: 206.0px;">
<col style=3D"width: 147.0px;">
<col style=3D"width: 131.0px;">
<col style=3D"width: 110.0px;">
<col style=3D"width: 180.0px;">
</colgroup>
<tbody>
<tr>
<td colspan=3D"8" class=3D"confluenceTd">
<p style=3D"text-align: right;"><strong>*TEMPO ESTIMADO: </strong>00:30</p>=
</td>
</tr>
<tr>
<td data-highlight-colour=3D"#ffe7e7" rowspan=3D"2" class=3D"confluenceTd">
<p><strong>Plano de</strong><br><strong>Retorno</strong></p></td>
<td class=3D"confluenceTd">
<p><strong>*Item</strong></p></td>
<td class=3D"confluenceTd">
<p><strong>*Atividade</strong></p></td>
<td class=3D"confluenceTd">
<p><strong>*Descri=C3=A7=C3=A3o</strong></p></td>
<td class=3D"confluenceTd">
<p><strong>*Respons=C3=A1vel</strong></p></td>
<td class=3D"confluenceTd">
<p><strong>*Hora in=C3=ADcio</strong></p></td>
<td class=3D"confluenceTd">
<p><strong>*Hora final</strong></p></td>
<td class=3D"confluenceTd">
<p><strong>Observa=C3=A7=C3=A3o</strong></p></td>
</tr>
<tr>
<td class=3D"confluenceTd">
<p>1</p></td>
<td class=3D"confluenceTd">
<p>Corre=C3=A7=C3=A3o para mapeamento</p></td>
<td class=3D"confluenceTd">
<p>Em caso de RollBack, somente voltar o mapeamento para o campo anterior.<=
/p></td>
<td class=3D"confluenceTd">
<p>Pedro Ribeiro</p></td>
<td class=3D"confluenceTd">
<p>18:30</p></td>
<td class=3D"confluenceTd">
<p>19:00</p></td>
<td class=3D"confluenceTd">
<p>Em RollBack, a corre=C3=A7=C3=A3o vai ser invalidada, sendo necess=C3=A1=
rio entender como realizar uma nova a=C3=A7=C3=A3o para remediar a problem=
=C3=A1tica.</p></td>
</tr>
</tbody>
</table>
</div></td>
</tr>
</tbody>
</table>
</div>
<p><br></p>
    </div>
</body>
</html>
------=_Part_0_1601909373.1753909312406
Content-Type: application/octet-stream
Content-Transfer-Encoding: base64
Content-Location: file:///C:/b312a30ffd65deef7621b1777f5f4cd413073e0a99e9b09ff61707ee9276d0d6
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=

------=_Part_0_1601909373.1753909312406
Content-Type: application/octet-stream
Content-Transfer-Encoding: base64
Content-Location: file:///C:/02aebe1817d106b93fea3cd4cb77744900386d0dde585df0994951ac75fa8da2
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------=_Part_0_1601909373.1753909312406
Content-Type: application/octet-stream
Content-Transfer-Encoding: base64
Content-Location: file:///C:/22811be4d0f6030c42417f736d0b02d9eb1b32174e217bb58eb2f85d4ba6fd8c
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------=_Part_0_1601909373.1753909312406--
