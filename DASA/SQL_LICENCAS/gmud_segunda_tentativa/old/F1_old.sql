--Valida Funcionarios com cargos especificos
(CustomProperty04 = 'Op Técnicos' or 
CustomProperty04 = 'Residente/Especializ') and 
IdentityType = 'Primary' and 
IsInActive = '0' and
IsExternal = '0' and
EntryDate >= '02/01/24' and
not exists (select 1 from PersonWantsOrg 
	where PersonWantsOrg.UID_PersonOrdered = person.uid_person
	and DisplayOrg like 'Licença%' 
	and OrderState = 'Assigned') 
--Valida Terceiros que não solicitaram licença
or
IdentityType = 'Primary' and 
IsExternal = '1' and
IsInActive = '0' and
EntryDate >= '02/01/24' and
not exists (select 1 from PersonWantsOrg 
	where PersonWantsOrg.UID_PersonOrdered = person.uid_person
	and DisplayOrg like 'Licença%' 
	and OrderState = 'Assigned') 
--Valida funcionarios que não receberam E1/E3 e não solicitaram licença
or 
IdentityType = 'Primary' and 
IsInActive = '0' and
IsExternal = '0' and
EntryDate >= '02/01/24' and
not exists (select 1 from PersonWantsOrg 
	where PersonWantsOrg.UID_PersonOrdered = person.uid_person
	and DisplayOrg like 'Licença%' 
	and OrderState = 'Assigned') 
and 
not exists (select 1 from PersonInOrg 
	where PersonInOrg.UID_Person = person.uid_person
	and (uid_org = '5ee40f26-37ce-4ce6-9f72-07e1c30a7a4b' or uid_org = 'aadf5450-1899-40df-bbc9-733b3215b997'))