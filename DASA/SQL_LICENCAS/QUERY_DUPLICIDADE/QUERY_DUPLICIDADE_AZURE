SELECT DISTINCT u.displayname, g.uid_aaduser
FROM aaduseringroup g
JOIN aaduser u ON g.uid_aaduser = u.uid_aaduser
WHERE g.uid_aadgroup IN (
    '65be895c-0d01-4e71-9603-4c38fa58c6b7',
    'd9b5e55c-8fb0-42fc-8fba-66765e0fda14',
    'ac342437-820d-4709-95a8-9f76bc5664b3',
    'd95472f4-9f56-4dca-bd76-de58ecea6d26'
)
AND g.uid_aaduser IN (
    SELECT uid_aaduser
    FROM aaduseringroup
    WHERE uid_aadgroup IN (
        '65be895c-0d01-4e71-9603-4c38fa58c6b7',
        'd9b5e55c-8fb0-42fc-8fba-66765e0fda14',
        'ac342437-820d-4709-95a8-9f76bc5664b3',
        'd95472f4-9f56-4dca-bd76-de58ecea6d26'
    )
    GROUP BY uid_aaduser
    HAVING COUNT(*) > 1
)
AND g.uid_aaduser NOT IN (
    SELECT uid_aaduser
    FROM aaduseringroup
    WHERE xmarkedfordeletion = 2
)
ORDER BY u.displayname;
