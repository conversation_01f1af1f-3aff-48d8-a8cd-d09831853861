(CustomProperty04 = 'Op Administrativos' or 
CustomProperty04 = 'Estagiários') and 
IdentityType = 'Primary' and 
IsExternal = '0' and
IsInActive = '0' and
EntryDate >= '02/01/24' and
not exists (select 1 from PersonWantsOrg 
	where PersonWantsOrg.UID_PersonOrdered = person.uid_person
	and DisplayOrg like 'Licença%' 
	and OrderState = 'Assigned')
or
(CustomProperty04 = 'Residente/Especializ' or 
CustomProperty04 = 'Aprendiz') and 
IdentityType = 'Primary' and 
IsExternal = '0' and
IsInActive = '0' and
EntryDate >= '05/28/24' and
not exists (select 1 from PersonWantsOrg 
	where PersonWantsOrg.UID_PersonOrdered = person.uid_person
	and DisplayOrg like 'Licença%' 
	and OrderState = 'Assigned')