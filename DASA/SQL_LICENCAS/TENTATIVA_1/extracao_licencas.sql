-- extracao das business roles

SELECT * 
FROM PersonInOrg 
WHERE UID_Org 
	in 
	(
	'85171ec1-c0a8-4844-a4f7-ab185199d6e1', 
	'bd8dede1-f570-482b-89e1-c09fc34ceaa6', 
	'aadf5450-1899-40df-bbc9-733b3215b997', 
	'8f7e33bc-eacb-43fa-b999-94feeb5aa3cd', 
	'********-95ac-4790-bed0-a4e9f174306d',
	'5ee40f26-37ce-4ce6-9f72-07e1c30a7a4b'
	)

	18867
	18472
	17871

-- extracao dos grupos do ad associados as contas

SELECT *
FROM ADSAccountInADSGroup
WHERE UID_ADSGroup
	IN
	(
	'e6ad1ae8-4f91-4883-a20c-77c926768722',
	'd0e68a0d-be51-430d-a2a0-eb17225dbe2',
	'7581fdfb-e5f2-4ee6-bf0b-c41492439bdb',
	'fde33c0a-ea72-4497-9575-30a688af4c98'
	)
	AND
	xMarkedForDeletion = 0

	90587

Nº da RDM: CHG0063973 - Correção Em Atribuição de Licenças Office 365 - DA - IDM
Janela: 28/11/2024 as 23:00:00 - 29/11/2024 as 02:30:00
Vertical: DASA Medicina Diagnóstica
Tipo: Normal
Status: Iniciada
Conclusão: Bem Sucedida








SELECT
    p.UID_ADSAccount,
    COUNT(DISTINCT pi.UID_ADSGroup) AS OrgCount
FROM
    ADSAccountInADSGroup pi
JOIN
    ADSAccount p
    ON pi.UID_ADSAccount = p.UID_ADSAccount
WHERE
    pi.UID_ADSGroup IN (
        '7581fdfb-e5f2-4ee6-bf0b-c41492439bdb', 
        'e6ad1ae8-4f91-4883-a20c-77c926768722', 
        'd0e68a0d-be51-430d-a2a0-eb17225dbe23', 
        'fde33c0a-ea72-4497-9575-30a688af4c98'
    )
AND
	p.AccountDisabled = 'False'
AND
	p.xMarkedforDeletion = '0'
AND
	pi.xIsInEffect = 'True'
GROUP BY
    p.UID_ADSAccount
HAVING
    COUNT(DISTINCT pi.UID_ADSGroup) > 1
