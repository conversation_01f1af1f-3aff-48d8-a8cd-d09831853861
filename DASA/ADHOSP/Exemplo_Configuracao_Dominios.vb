'-> =====================================================
'-> EXEMPLO DE CONFIGURAÇÃO DE DOMÍNIOS
'-> =====================================================
'-> Este arquivo mostra exemplos práticos de como configurar
'-> diferentes domínios e suas Account Definitions

'-> Substitua esta função na versão flexível do script
Private Function GetAccountDefinitionsForDomain(domainUID As String) As List(Of String)
    Dim accountDefs As New List(Of String)
    
    If String.IsNullOrEmpty(domainUID) Then
        '-> Se domínio não identificado, usar Account Definitions padrão
        Return GetDefaultAccountDefinitions()
    End If
    
    '-> Obter nome do domínio para facilitar configuração
    Dim domainName As String = GetDomainName(domainUID)
    
    '-> =====================================================
    '-> CONFIGURAÇÃO POR DOMÍNIO - EXEMPLOS PRÁTICOS
    '-> =====================================================
    Select Case domainName.ToUpper()
        
        '-> EXEMPLO 1: Domínio Principal da Empresa
        Case "EMPRESA.LOCAL", "EMPRESA", "CORP.LOCAL"
            '-> Account Definitions para o domínio principal
            accountDefs.Add("AD-User")              ' Usuários padrão da empresa
            accountDefs.Add("AD-Manager")           ' Gerentes da empresa
            accountDefs.Add("AD-Admin")             ' Administradores da empresa
            accountDefs.Add("CORP-Standard")        ' Account Definition corporativa
            
        '-> EXEMPLO 2: Domínio de Filial
        Case "FILIAL.LOCAL", "FILIAL", "BRANCH.LOCAL"
            '-> Account Definitions específicas da filial
            accountDefs.Add("AD-User-Filial")       ' Usuários da filial
            accountDefs.Add("AD-Supervisor-Filial") ' Supervisores da filial
            accountDefs.Add("FILIAL-Standard")      ' Account Definition da filial
            
        '-> EXEMPLO 3: Domínio de Terceiros/Parceiros
        Case "TERCEIROS.LOCAL", "PARTNERS.LOCAL", "EXTERNAL.LOCAL"
            '-> Account Definitions para terceiros
            '-> Busca configuração dinâmica do sistema
            Dim terceiroAccountDef As String = GetConfigParameter("Custom\AD\AccountDefforTerceiro")
            If Not String.IsNullOrEmpty(terceiroAccountDef) Then
                accountDefs.Add(terceiroAccountDef)
            End If
            accountDefs.Add("AD-Terceiros")         ' Account Definition para terceiros
            accountDefs.Add("AD-Parceiro")          ' Account Definition para parceiros
            accountDefs.Add("EXTERNAL-User")        ' Usuários externos
            
        '-> EXEMPLO 4: Domínio de Desenvolvimento
        Case "DEV.LOCAL", "DESENVOLVIMENTO.LOCAL", "DEVELOPMENT.LOCAL"
            '-> Account Definitions para ambiente de desenvolvimento
            accountDefs.Add("AD-Developer")         ' Desenvolvedores
            accountDefs.Add("AD-DevManager")        ' Gerentes de desenvolvimento
            accountDefs.Add("AD-Tester")            ' Testadores
            accountDefs.Add("DEV-Standard")         ' Account Definition padrão do dev
            
        '-> EXEMPLO 5: Domínio de Produção
        Case "PROD.LOCAL", "PRODUCAO.LOCAL", "PRODUCTION.LOCAL"
            '-> Account Definitions para ambiente de produção
            accountDefs.Add("AD-ProdUser")          ' Usuários de produção
            accountDefs.Add("AD-ProdAdmin")         ' Administradores de produção
            accountDefs.Add("AD-Operator")          ' Operadores de sistema
            accountDefs.Add("PROD-Standard")        ' Account Definition padrão de produção
            
        '-> EXEMPLO 6: Domínio de DMZ/Perímetro
        Case "DMZ.LOCAL", "PERIMETER.LOCAL", "EDGE.LOCAL"
            '-> Account Definitions para zona desmilitarizada
            accountDefs.Add("AD-DMZUser")           ' Usuários da DMZ
            accountDefs.Add("AD-EdgeAdmin")         ' Administradores de borda
            accountDefs.Add("DMZ-Service")          ' Contas de serviço da DMZ
            
        '-> EXEMPLO 7: Domínio de Teste/QA
        Case "TEST.LOCAL", "QA.LOCAL", "TESTE.LOCAL"
            '-> Account Definitions para ambiente de teste
            accountDefs.Add("AD-TestUser")          ' Usuários de teste
            accountDefs.Add("AD-QAManager")         ' Gerentes de QA
            accountDefs.Add("AD-TestAdmin")         ' Administradores de teste
            accountDefs.Add("QA-Standard")          ' Account Definition padrão de QA
            
        '-> EXEMPLO 8: Domínio Regional (Múltiplas Regiões)
        Case "NORTE.LOCAL", "REGIAO-NORTE.LOCAL"
            '-> Account Definitions para região Norte
            accountDefs.Add("AD-User-Norte")        ' Usuários da região Norte
            accountDefs.Add("AD-Manager-Norte")     ' Gerentes da região Norte
            accountDefs.Add("NORTE-Standard")       ' Account Definition regional
            
        Case "SUL.LOCAL", "REGIAO-SUL.LOCAL"
            '-> Account Definitions para região Sul
            accountDefs.Add("AD-User-Sul")          ' Usuários da região Sul
            accountDefs.Add("AD-Manager-Sul")       ' Gerentes da região Sul
            accountDefs.Add("SUL-Standard")         ' Account Definition regional
            
        '-> EXEMPLO 9: Domínio por Departamento
        Case "TI.LOCAL", "IT.LOCAL", "TECNOLOGIA.LOCAL"
            '-> Account Definitions para departamento de TI
            accountDefs.Add("AD-ITUser")            ' Usuários de TI
            accountDefs.Add("AD-ITManager")         ' Gerentes de TI
            accountDefs.Add("AD-SysAdmin")          ' Administradores de sistema
            accountDefs.Add("IT-Standard")          ' Account Definition padrão de TI
            
        Case "RH.LOCAL", "HR.LOCAL", "RECURSOS-HUMANOS.LOCAL"
            '-> Account Definitions para departamento de RH
            accountDefs.Add("AD-HRUser")            ' Usuários de RH
            accountDefs.Add("AD-HRManager")         ' Gerentes de RH
            accountDefs.Add("HR-Standard")          ' Account Definition padrão de RH
            
        Case "FINANCEIRO.LOCAL", "FINANCE.LOCAL", "CONTABILIDADE.LOCAL"
            '-> Account Definitions para departamento Financeiro
            accountDefs.Add("AD-FinanceUser")       ' Usuários do financeiro
            accountDefs.Add("AD-FinanceManager")    ' Gerentes do financeiro
            accountDefs.Add("AD-Accountant")        ' Contadores
            accountDefs.Add("FINANCE-Standard")     ' Account Definition padrão financeiro
            
        '-> EXEMPLO 10: Configuração Híbrida (Azure AD + On-Premises)
        Case "AZUREAD.LOCAL", "CLOUD.LOCAL", "HYBRID.LOCAL"
            '-> Account Definitions para ambiente híbrido
            accountDefs.Add("AAD-User")             ' Usuários do Azure AD
            accountDefs.Add("AAD-Manager")          ' Gerentes do Azure AD
            accountDefs.Add("HYBRID-Standard")      ' Account Definition híbrida
            '-> Também incluir Account Definitions on-premises como fallback
            accountDefs.Add("AD-User")              ' Fallback para AD local
            
        '-> EXEMPLO 11: Configuração com Múltiplas Account Definitions por Tipo de Usuário
        Case "MULTINIVEL.LOCAL", "COMPLEX.LOCAL"
            '-> Diferentes Account Definitions baseadas em níveis de acesso
            accountDefs.Add("AD-User-L1")           ' Usuários nível 1 (básico)
            accountDefs.Add("AD-User-L2")           ' Usuários nível 2 (intermediário)
            accountDefs.Add("AD-User-L3")           ' Usuários nível 3 (avançado)
            accountDefs.Add("AD-Manager-L1")        ' Gerentes nível 1
            accountDefs.Add("AD-Manager-L2")        ' Gerentes nível 2
            accountDefs.Add("AD-Admin-L1")          ' Administradores nível 1
            accountDefs.Add("AD-Admin-L2")          ' Administradores nível 2
            
        Case Else
            '-> DOMÍNIO NÃO CONFIGURADO
            '-> Tenta buscar Account Definition padrão do domínio na tabela ADSDomain
            Dim domainDefaultAccountDef As String = GetDomainDefaultAccountDefinition(domainUID)
            If Not String.IsNullOrEmpty(domainDefaultAccountDef) Then
                accountDefs.Add(domainDefaultAccountDef)
            End If
            
            '-> Adicionar Account Definitions padrão como fallback
            accountDefs.AddRange(GetDefaultAccountDefinitions())
            
            '-> Log para identificar domínios não configurados (opcional)
            ' System.Diagnostics.Debug.WriteLine("Domínio não configurado: " & domainName & " (" & domainUID & ")")
            
    End Select
    
    '-> Se nenhuma Account Definition foi configurada, usar padrão
    If accountDefs.Count = 0 Then
        accountDefs.AddRange(GetDefaultAccountDefinitions())
    End If
    
    Return accountDefs
End Function

'-> =====================================================
'-> EXEMPLO DE CONFIGURAÇÃO DE ACCOUNT DEFINITIONS PADRÃO
'-> =====================================================
Private Function GetDefaultAccountDefinitions() As List(Of String)
    Dim defaultDefs As New List(Of String)
    
    '-> Account Definitions que funcionam na maioria dos domínios
    defaultDefs.Add("AD-User")               ' Account Definition mais comum
    defaultDefs.Add("AD-Standard")           ' Account Definition padrão alternativa
    
    '-> Buscar configuração de terceiros se existir
    Dim terceiroAccountDef As String = GetConfigParameter("Custom\AD\AccountDefforTerceiro")
    If Not String.IsNullOrEmpty(terceiroAccountDef) Then
        defaultDefs.Add(terceiroAccountDef)
    End If
    
    '-> Buscar outras configurações personalizadas
    Dim customAccountDef As String = GetConfigParameter("Custom\AD\DefaultAccountDef")
    If Not String.IsNullOrEmpty(customAccountDef) Then
        defaultDefs.Add(customAccountDef)
    End If
    
    Return defaultDefs
End Function

'-> =====================================================
'-> DICAS DE CONFIGURAÇÃO
'-> =====================================================
'
' 1. NOMES DE DOMÍNIO:
'    - Use tanto o FQDN quanto o nome curto: "EMPRESA.LOCAL", "EMPRESA"
'    - Considere variações: "CORP.LOCAL", "CORPORATIVO.LOCAL"
'
' 2. ACCOUNT DEFINITIONS:
'    - Use nomes descritivos: "AD-User-Filial" em vez de "ADF1"
'    - Mantenha consistência: sempre "AD-" como prefixo
'    - Documente o propósito de cada uma
'
' 3. FALLBACKS:
'    - Sempre configure Account Definitions padrão
'    - Use o Case Else para domínios não configurados
'    - Considere buscar configurações dinâmicas
'
' 4. MANUTENÇÃO:
'    - Comente cada seção claramente
'    - Agrupe domínios relacionados
'    - Use constantes para Account Definitions repetidas
'
' 5. TESTE:
'    - Teste cada domínio individualmente
'    - Verifique fallbacks funcionam
'    - Valide Account Definitions existem no sistema
