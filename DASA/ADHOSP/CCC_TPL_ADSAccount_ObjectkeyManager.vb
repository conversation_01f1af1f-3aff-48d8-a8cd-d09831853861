Function CCC_tpl_ADSAccount_ObjectkeyManager(strUIDLineManager As String) As String

      Dim strResult As String = ""
      Try
        '-> Only if there is a Line Manager on person configured
        If Not String.IsNullOrEmpty(strUIDLineManager) Then
          '-> Get the domain of the current account to find manager account in the same domain
          Dim strCurrentAccountDomain As String = ""

          ' Try multiple ways to get the current account's domain
          ' Method 1: From Variables (template context)
          Try
            Dim domainValue As Object = Variables("UID_ADSDomain")
            If domainValue IsNot Nothing AndAlso Not String.IsNullOrEmpty(domainValue.ToString()) Then
              strCurrentAccountDomain = domainValue.ToString()
            End If
          Catch
            ' Continue to next method
          End Try

          ' Method 2: From Entity (if available) - only if not found yet
          If String.IsNullOrEmpty(strCurrentAccountDomain) Then
            Try
              Dim entityValue As Object = Entity.GetValue("UID_ADSDomain")
              If entityValue IsNot Nothing AndAlso Not String.IsNullOrEmpty(entityValue.ToString()) Then
                strCurrentAccountDomain = entityValue.ToString()
              End If
            Catch
              ' Continue to next method
            End Try
          End If

          ' Method 3: From current person's existing AD accounts - only if not found yet
          If String.IsNullOrEmpty(strCurrentAccountDomain) Then
            Try
              Dim personValue As Object = Variables("UID_Person")
              If personValue IsNot Nothing AndAlso Not String.IsNullOrEmpty(personValue.ToString()) Then
                Dim strCurrentPerson As String = personValue.ToString()
                Dim qCurrentDomain As Query = Query.From("ADSAccount") _
                                    .Where(String.Format("uid_person = '{0}'", strCurrentPerson)) _
                                    .Select("UID_ADSDomain")
                Dim colCurrentDomain As IEntityCollection = Session.Source.GetCollection(qCurrentDomain)
                If colCurrentDomain.Count > 0 Then
                  strCurrentAccountDomain = colCurrentDomain(0).GetValue("UID_ADSDomain").String
                End If
              End If
            Catch
              ' If all methods fail, strCurrentAccountDomain remains empty
              ' and we'll use fallback logic
            End Try
          End If

          Dim ColADAccounts As IEntityCollection
          Dim qADSAccount As Query

          '-> Build the query based on whether we have domain information
          If Not String.IsNullOrEmpty(strCurrentAccountDomain) Then
            '-> PREFERRED: Search for ANY manager account in the same domain
            '-> This ensures we get the manager's account from the same AD domain as the current account
            qADSAccount = Query.From("ADSAccount") _
                          .Where(String.Format("uid_person = '{0}' and UID_ADSDomain = '{1}'", strUIDLineManager, strCurrentAccountDomain)) _
                          .Select("xobjectkey")
          Else
            '-> FALLBACK: If current domain is unknown, get any manager account
            qADSAccount = Query.From("ADSAccount") _
                          .Where(String.Format("uid_person = '{0}'", strUIDLineManager)) _
                          .Select("xobjectkey", "UID_ADSDomain")
          End If

          ColADAccounts = Session.Source.GetCollection(qADSAccount)

          '-> Process the results to find the best manager account
          If ColADAccounts.Count > 0 Then
            If Not String.IsNullOrEmpty(strCurrentAccountDomain) Then
              '-> Domain-specific search was successful, use the result
              strResult = ColADAccounts(0).GetValue("xobjectkey").String
            Else
              '-> Fallback processing: we have manager accounts but don't know current domain
              '-> Use the first available manager account
              '-> TODO: Could be enhanced to prefer accounts from default domain or use additional logic
              strResult = ColADAccounts(0).GetValue("xobjectkey").String
            End If
          End If
          '-> If no manager account found (either in same domain or at all), strResult remains empty
          '-> This satisfies the requirement to leave the field empty when no manager account is found
        End If
      Catch ex As Exception
        Throw New Exception("Error in script CCC_tpl_ADSAccount_ObjectkeyManager: " & ex.Message, ex)

      Finally
        CCC_tpl_ADSAccount_ObjectkeyManager = strResult

      End Try

End Function