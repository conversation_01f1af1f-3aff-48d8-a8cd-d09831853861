Function CCC_tpl_ADSAccount_ObjectkeyManager(strUIDLineManager As String) As String

    Dim strResult As String = ""
    Try
        '-> Only if there is a Line Manager on person configured
        If Not String.IsNullOrEmpty(strUIDLineManager) Then
            '-> Get the domain of the current account
            Dim strCurrentAccountDomain As String = GetCurrentAccountDomain()
            
            '-> Get Account Definitions for the current domain
            Dim accountDefinitions As List(Of String) = GetAccountDefinitionsForDomain(strCurrentAccountDomain)
            
            '-> Search for manager account
            Dim ColADAccounts As IEntityCollection = FindManagerAccount(strUIDLineManager, strCurrentAccountDomain, accountDefinitions)
            
            '-> Return the first valid result
            If ColADAccounts.Count > 0 Then
                strResult = ColADAccounts(0).GetValue("xobjectkey").String
            End If
            '-> If no manager account found, strResult remains empty (as requested)
        End If
    Catch ex As Exception
        Throw New Exception("Error in script CCC_tpl_ADSAccount_ObjectkeyManager: " & ex.Message, ex)
    End Try
    
    Return strResult

End Function

'-> =====================================================
'-> CONFIGURAÇÃO DE ACCOUNT DEFINITIONS POR DOMÍNIO
'-> =====================================================
'-> ADICIONE NOVOS DOMÍNIOS E SUAS ACCOUNT DEFINITIONS AQUI
'-> Esta função centraliza toda a configuração de Account Definitions
Private Function GetAccountDefinitionsForDomain(domainUID As String) As List(Of String)
    Dim accountDefs As New List(Of String)
    
    If String.IsNullOrEmpty(domainUID) Then
        '-> Se domínio não identificado, usar Account Definitions padrão
        Return GetDefaultAccountDefinitions()
    End If
    
    '-> Obter nome do domínio para facilitar configuração
    Dim domainName As String = GetDomainName(domainUID)
    
    '-> =====================================================
    '-> CONFIGURAÇÃO POR DOMÍNIO - EDITE AQUI PARA ADICIONAR NOVOS DOMÍNIOS
    '-> =====================================================
    Select Case domainName.ToUpper()
        
        Case "DOMAIN1.COM", "DOMAIN1"  ' <- SUBSTITUA pelo nome do seu primeiro domínio
            '-> Account Definitions para DOMAIN1
            accountDefs.Add("AD-User")           ' Account Definition padrão
            accountDefs.Add("AD-Admin")          ' Account Definition de admin (se existir)
            ' accountDefs.Add("DOMAIN1-User")    ' Account Definition específica do domínio (descomente se necessário)
            
        Case "DOMAIN2.COM", "DOMAIN2"  ' <- SUBSTITUA pelo nome do seu segundo domínio
            '-> Account Definitions para DOMAIN2
            accountDefs.Add("AD-User-Domain2")   ' Account Definition específica do segundo domínio
            accountDefs.Add("AD-Standard")       ' Outra Account Definition do segundo domínio
            ' accountDefs.Add("DOMAIN2-Manager") ' Account Definition para gerentes (descomente se necessário)
            
        Case "TERCEIROS.COM", "TERCEIROS"  ' <- Domínio de terceiros
            '-> Account Definitions para terceiros
            Dim terceiroAccountDef As String = GetConfigParameter("Custom\AD\AccountDefforTerceiro")
            If Not String.IsNullOrEmpty(terceiroAccountDef) Then
                accountDefs.Add(terceiroAccountDef)
            End If
            accountDefs.Add("AD-Terceiros")      ' Account Definition específica para terceiros
            
        Case Else
            '-> Domínio não configurado - usar Account Definitions padrão
            '-> Também tenta buscar Account Definition padrão do domínio na tabela ADSDomain
            Dim domainDefaultAccountDef As String = GetDomainDefaultAccountDefinition(domainUID)
            If Not String.IsNullOrEmpty(domainDefaultAccountDef) Then
                accountDefs.Add(domainDefaultAccountDef)
            End If
            
            '-> Adicionar Account Definitions padrão como fallback
            accountDefs.AddRange(GetDefaultAccountDefinitions())
            
    End Select
    
    '-> Se nenhuma Account Definition foi configurada, usar padrão
    If accountDefs.Count = 0 Then
        accountDefs.AddRange(GetDefaultAccountDefinitions())
    End If
    
    Return accountDefs
End Function

'-> =====================================================
'-> ACCOUNT DEFINITIONS PADRÃO
'-> =====================================================
'-> Edite esta função para alterar as Account Definitions padrão
Private Function GetDefaultAccountDefinitions() As List(Of String)
    Dim defaultDefs As New List(Of String)
    
    '-> Account Definitions padrão que funcionam em qualquer domínio
    defaultDefs.Add("AD-User")               ' Account Definition principal
    
    '-> Adicionar Account Definition de terceiros se configurada
    Dim terceiroAccountDef As String = GetConfigParameter("Custom\AD\AccountDefforTerceiro")
    If Not String.IsNullOrEmpty(terceiroAccountDef) Then
        defaultDefs.Add(terceiroAccountDef)
    End If
    
    Return defaultDefs
End Function

'-> =====================================================
'-> FUNÇÕES AUXILIARES
'-> =====================================================

Private Function GetCurrentAccountDomain() As String
    Dim strCurrentAccountDomain As String = ""
    
    ' Method 1: From Variables (template context)
    Try
        Dim domainValue As Object = Variables("UID_ADSDomain")
        If domainValue IsNot Nothing AndAlso Not String.IsNullOrEmpty(domainValue.ToString()) Then
            Return domainValue.ToString()
        End If
    Catch
        ' Continue to next method
    End Try
    
    ' Method 2: From Entity (if available)
    Try
        Dim entityValue As Object = Entity.GetValue("UID_ADSDomain")
        If entityValue IsNot Nothing AndAlso Not String.IsNullOrEmpty(entityValue.ToString()) Then
            Return entityValue.ToString()
        End If
    Catch
        ' Continue to next method
    End Try
    
    ' Method 3: From current person's existing AD accounts
    Try
        Dim personValue As Object = Variables("UID_Person")
        If personValue IsNot Nothing AndAlso Not String.IsNullOrEmpty(personValue.ToString()) Then
            Dim strCurrentPerson As String = personValue.ToString()
            Dim qCurrentDomain As Query = Query.From("ADSAccount") _
                                .Where(String.Format("uid_person = '{0}'", strCurrentPerson)) _
                                .Select("UID_ADSDomain")
            Dim colCurrentDomain As IEntityCollection = Session.Source.GetCollection(qCurrentDomain)
            If colCurrentDomain.Count > 0 Then
                Return colCurrentDomain(0).GetValue("UID_ADSDomain").String
            End If
        End If
    Catch
        ' All methods failed
    End Try
    
    Return strCurrentAccountDomain
End Function

Private Function GetDomainName(domainUID As String) As String
    If String.IsNullOrEmpty(domainUID) Then Return ""
    
    Try
        Dim qDomain As Query = Query.From("ADSDomain") _
                              .Where(String.Format("UID_ADSDomain = '{0}'", domainUID)) _
                              .Select("Ident_Domain")
        Dim colDomain As IEntityCollection = Session.Source.GetCollection(qDomain)
        If colDomain.Count > 0 Then
            Return colDomain(0).GetValue("Ident_Domain").String
        End If
    Catch
        ' Return empty if error
    End Try
    
    Return ""
End Function

Private Function GetDomainDefaultAccountDefinition(domainUID As String) As String
    If String.IsNullOrEmpty(domainUID) Then Return ""
    
    Try
        Dim qDomain As Query = Query.From("ADSDomain") _
                              .Where(String.Format("UID_ADSDomain = '{0}' AND UID_TSBAccountDef IS NOT NULL", domainUID)) _
                              .Select("UID_TSBAccountDef")
        Dim colDomain As IEntityCollection = Session.Source.GetCollection(qDomain)
        If colDomain.Count > 0 Then
            Dim accountDefUID As String = colDomain(0).GetValue("UID_TSBAccountDef").String
            
            ' Buscar o nome da Account Definition
            Dim qAccountDef As Query = Query.From("TSBAccountDef") _
                                      .Where(String.Format("UID_TSBAccountDef = '{0}'", accountDefUID)) _
                                      .Select("Ident_TSBAccountDef")
            Dim colAccountDef As IEntityCollection = Session.Source.GetCollection(qAccountDef)
            If colAccountDef.Count > 0 Then
                Return colAccountDef(0).GetValue("Ident_TSBAccountDef").String
            End If
        End If
    Catch
        ' Return empty if error
    End Try
    
    Return ""
End Function

Private Function GetConfigParameter(parameterPath As String) As String
    Try
        Dim configValue As Object = Connection.GetConfigParm(parameterPath)
        If configValue IsNot Nothing Then
            Return configValue.ToString()
        End If
    Catch
        ' Return empty if config not found
    End Try
    Return ""
End Function

Private Function FindManagerAccount(managerUID As String, domainUID As String, accountDefinitions As List(Of String)) As IEntityCollection
    Dim qADSAccount As Query
    
    If Not String.IsNullOrEmpty(domainUID) AndAlso accountDefinitions.Count > 0 Then
        '-> PREFERRED: Search for manager account in the same domain with specific Account Definitions
        Dim accountDefFilter As String = BuildAccountDefinitionFilter(accountDefinitions)
        qADSAccount = Query.From("ADSAccount") _
                      .Where(String.Format("uid_person = '{0}' AND UID_ADSDomain = '{1}' AND ({2})", _
                             managerUID, domainUID, accountDefFilter)) _
                      .Select("xobjectkey")
    Else
        '-> FALLBACK: If current domain is unknown or no Account Definitions, get any manager account
        Dim defaultAccountDefs As List(Of String) = GetDefaultAccountDefinitions()
        If defaultAccountDefs.Count > 0 Then
            Dim accountDefFilter As String = BuildAccountDefinitionFilter(defaultAccountDefs)
            qADSAccount = Query.From("ADSAccount") _
                          .Where(String.Format("uid_person = '{0}' AND ({1})", managerUID, accountDefFilter)) _
                          .Select("xobjectkey", "UID_ADSDomain")
        Else
            '-> Last resort: any account for the manager
            qADSAccount = Query.From("ADSAccount") _
                          .Where(String.Format("uid_person = '{0}'", managerUID)) _
                          .Select("xobjectkey", "UID_ADSDomain")
        End If
    End If
    
    Return Session.Source.GetCollection(qADSAccount)
End Function

Private Function BuildAccountDefinitionFilter(accountDefinitions As List(Of String)) As String
    If accountDefinitions.Count = 0 Then Return "1=1"
    
    Dim quotedDefs As New List(Of String)
    For Each accountDef As String In accountDefinitions
        quotedDefs.Add("'" & accountDef & "'")
    Next
    
    Return String.Format("UID_TSBAccountDef IN (SELECT UID_TSBAccountDef FROM TSBAccountDef WHERE Ident_TSBAccountDef IN ({0}))", _
                        String.Join(",", quotedDefs))
End Function
