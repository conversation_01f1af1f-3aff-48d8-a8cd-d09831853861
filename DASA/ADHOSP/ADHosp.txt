ADHosp

- Colaboradores Impar vindos do SAP precisam ser criados com J + CPF

- Colaboradores Impar vindos do portal devem ser criados no ADHosp também com T + CPF mesmo

- Médicos segregados igualmente

- Fazer mapeamento de campos

- Regras de provisionamento e desprovisionamento

- Definição de OUs e grupos padrão

- Parâmetros de criação de email, licenças e políticas

- Email de desligados Américas

- Portal de acessos exclusivo para américas

- Definições de kit básico para américas

- Não recebemos a base completa de empresas que devem ser segregadas para colaboradores Américas, somente tendo certeza para a empresa Impar que está sendo preenchido no campo FirmPartner da identidade

- Será possível associar assim que a conta do AD no domínio equivalente para o colaborador for criada

- Problemáticas de recontratação não estão englobados na implementação do conector e devem ser correções apartadas do projeto

- Atendemos uma solicitação anterior que disponibilizou a criação de médicos com o sufixo M, além da retirada da interpretação do colaborador como externo no IDM. Além disso, terá a adaptação para criação de contas no ADHosp com @AmericasMed, como estabelecido por email anteriormente

CAMPOS QUE DEVO ALTERAR

- Campo SAMAccountName e todos os campos que envolvem a credencial

- Campo Mail para atualizar corretamente as contas do ADHosp para criar com @americasmed

- ObjectKeyManager para procurar corretamente o gerente do colaborador no ADHosp caso tenha conta nesse domínio, em caso negativo, não alterar o valor do campo

FLUXOS DE CONCESSÃO DE ACESSOS
