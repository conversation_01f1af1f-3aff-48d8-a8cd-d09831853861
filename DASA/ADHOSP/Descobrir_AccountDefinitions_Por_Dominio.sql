-- =====================================================
-- SCRIPT PARA DESCOBRIR ACCOUNT DEFINITIONS POR DOMÍNIO
-- =====================================================
-- Execute este script para identificar quais Account Definitions
-- são usadas em cada domínio do seu ambiente

-- =====================================================
-- 1. DOMÍNIOS E SUAS ACCOUNT DEFINITIONS PADRÃO
-- =====================================================
PRINT '=== DOMÍNIOS E ACCOUNT DEFINITIONS PADRÃO ==='
SELECT 
    d.Ident_Domain AS 'Nome do Domínio',
    d.UID_ADSDomain AS 'UID do Domínio',
    CASE 
        WHEN d.UID_TSBAccountDef IS NOT NULL THEN tad.Ident_TSBAccountDef
        ELSE 'NÃO CONFIGURADO'
    END AS 'Account Definition Padrão do Domínio'
FROM ADSDomain d
LEFT JOIN TSBAccountDef tad ON d.UID_TSBAccountDef = tad.UID_TSBAccountDef
WHERE d.XMarkedForDeletion = 0
ORDER BY d.Ident_Domain;

-- =====================================================
-- 2. ACCOUNT DEFINITIONS UTILIZADAS POR DOMÍNIO
-- =====================================================
PRINT ''
PRINT '=== ACCOUNT DEFINITIONS UTILIZADAS POR DOMÍNIO ==='
SELECT 
    d.Ident_Domain AS 'Domínio',
    tad.Ident_TSBAccountDef AS 'Account Definition',
    COUNT(a.UID_ADSAccount) AS 'Quantidade de Contas',
    COUNT(CASE WHEN a.UID_Person IS NOT NULL THEN 1 END) AS 'Contas com Pessoa',
    COUNT(CASE WHEN a.AccountDisabled = 0 THEN 1 END) AS 'Contas Ativas'
FROM ADSDomain d
INNER JOIN ADSAccount a ON d.UID_ADSDomain = a.UID_ADSDomain AND a.XMarkedForDeletion = 0
INNER JOIN TSBAccountDef tad ON a.UID_TSBAccountDef = tad.UID_TSBAccountDef
WHERE d.XMarkedForDeletion = 0
GROUP BY d.Ident_Domain, tad.Ident_TSBAccountDef, d.UID_ADSDomain
ORDER BY d.Ident_Domain, tad.Ident_TSBAccountDef;

-- =====================================================
-- 3. ACCOUNT DEFINITIONS MAIS COMUNS POR DOMÍNIO
-- =====================================================
PRINT ''
PRINT '=== ACCOUNT DEFINITIONS MAIS COMUNS POR DOMÍNIO ==='
WITH AccountDefStats AS (
    SELECT 
        d.Ident_Domain,
        d.UID_ADSDomain,
        tad.Ident_TSBAccountDef,
        COUNT(a.UID_ADSAccount) AS QtdContas,
        ROW_NUMBER() OVER (PARTITION BY d.UID_ADSDomain ORDER BY COUNT(a.UID_ADSAccount) DESC) AS Ranking
    FROM ADSDomain d
    INNER JOIN ADSAccount a ON d.UID_ADSDomain = a.UID_ADSDomain AND a.XMarkedForDeletion = 0
    INNER JOIN TSBAccountDef tad ON a.UID_TSBAccountDef = tad.UID_TSBAccountDef
    WHERE d.XMarkedForDeletion = 0
    GROUP BY d.Ident_Domain, d.UID_ADSDomain, tad.Ident_TSBAccountDef
)
SELECT 
    Ident_Domain AS 'Domínio',
    Ident_TSBAccountDef AS 'Account Definition Mais Usada',
    QtdContas AS 'Quantidade de Contas'
FROM AccountDefStats
WHERE Ranking = 1
ORDER BY Ident_Domain;

-- =====================================================
-- 4. PESSOAS COM GERENTES EM DOMÍNIOS DIFERENTES
-- =====================================================
PRINT ''
PRINT '=== PESSOAS COM GERENTES EM DOMÍNIOS DIFERENTES ==='
WITH ManagerDomainMismatch AS (
    SELECT 
        sub.CentralAccount AS SubordinateLogin,
        mgr.CentralAccount AS ManagerLogin,
        sub_dom.Ident_Domain AS SubordinateDomain,
        mgr_dom.Ident_Domain AS ManagerDomain,
        sub_tad.Ident_TSBAccountDef AS SubordinateAccountDef,
        mgr_tad.Ident_TSBAccountDef AS ManagerAccountDef
    FROM Person sub
    INNER JOIN Person mgr ON sub.UID_PersonHead = mgr.UID_Person
    INNER JOIN ADSAccount sub_acc ON sub.UID_Person = sub_acc.UID_Person
    INNER JOIN ADSAccount mgr_acc ON mgr.UID_Person = mgr_acc.UID_Person
    INNER JOIN ADSDomain sub_dom ON sub_acc.UID_ADSDomain = sub_dom.UID_ADSDomain
    INNER JOIN ADSDomain mgr_dom ON mgr_acc.UID_ADSDomain = mgr_dom.UID_ADSDomain
    INNER JOIN TSBAccountDef sub_tad ON sub_acc.UID_TSBAccountDef = sub_tad.UID_TSBAccountDef
    INNER JOIN TSBAccountDef mgr_tad ON mgr_acc.UID_TSBAccountDef = mgr_tad.UID_TSBAccountDef
    WHERE sub.XMarkedForDeletion = 0
        AND mgr.XMarkedForDeletion = 0
        AND sub_acc.XMarkedForDeletion = 0
        AND mgr_acc.XMarkedForDeletion = 0
        AND sub_acc.UID_ADSDomain != mgr_acc.UID_ADSDomain -- Domínios diferentes
)
SELECT TOP 20
    SubordinateLogin AS 'Login Subordinado',
    SubordinateDomain AS 'Domínio Subordinado',
    SubordinateAccountDef AS 'AccountDef Subordinado',
    ManagerLogin AS 'Login Gerente',
    ManagerDomain AS 'Domínio Gerente',
    ManagerAccountDef AS 'AccountDef Gerente',
    'CAMPO FICARÁ VAZIO' AS 'Resultado Script'
FROM ManagerDomainMismatch
ORDER BY SubordinateLogin;

-- =====================================================
-- 5. SUGESTÕES DE CONFIGURAÇÃO PARA O SCRIPT
-- =====================================================
PRINT ''
PRINT '=== SUGESTÕES DE CONFIGURAÇÃO ==='

-- Gerar sugestões de Case para cada domínio
SELECT 
    'Case "' + UPPER(d.Ident_Domain) + '", "' + UPPER(REPLACE(d.Ident_Domain, '.LOCAL', '')) + '"' AS 'Linha Case Sugerida',
    '    accountDefs.Add("' + tad.Ident_TSBAccountDef + '")  '' ' + CAST(COUNT(a.UID_ADSAccount) AS VARCHAR(10)) + ' contas' AS 'Linha AccountDef Sugerida'
FROM ADSDomain d
INNER JOIN ADSAccount a ON d.UID_ADSDomain = a.UID_ADSDomain AND a.XMarkedForDeletion = 0
INNER JOIN TSBAccountDef tad ON a.UID_TSBAccountDef = tad.UID_TSBAccountDef
WHERE d.XMarkedForDeletion = 0
GROUP BY d.Ident_Domain, tad.Ident_TSBAccountDef
HAVING COUNT(a.UID_ADSAccount) >= 5  -- Apenas Account Definitions com pelo menos 5 contas
ORDER BY d.Ident_Domain, COUNT(a.UID_ADSAccount) DESC;

-- =====================================================
-- 6. TODAS AS ACCOUNT DEFINITIONS DISPONÍVEIS
-- =====================================================
PRINT ''
PRINT '=== TODAS AS ACCOUNT DEFINITIONS DISPONÍVEIS ==='
SELECT 
    tad.Ident_TSBAccountDef AS 'Account Definition',
    tad.Description AS 'Descrição',
    COUNT(a.UID_ADSAccount) AS 'Total de Contas Usando',
    COUNT(DISTINCT a.UID_ADSDomain) AS 'Domínios que Usam'
FROM TSBAccountDef tad
LEFT JOIN ADSAccount a ON tad.UID_TSBAccountDef = a.UID_TSBAccountDef AND a.XMarkedForDeletion = 0
WHERE tad.XMarkedForDeletion = 0
GROUP BY tad.Ident_TSBAccountDef, tad.Description
ORDER BY COUNT(a.UID_ADSAccount) DESC;

-- =====================================================
-- 7. CONFIGURAÇÕES DO SISTEMA RELACIONADAS
-- =====================================================
PRINT ''
PRINT '=== CONFIGURAÇÕES DO SISTEMA ==='
SELECT 
    'Custom\AD\AccountDefforTerceiro' AS 'Parâmetro',
    ISNULL(
        (SELECT TOP 1 Value FROM QBMConfigParm WHERE Path = 'Custom\AD\AccountDefforTerceiro'),
        'NÃO CONFIGURADO'
    ) AS 'Valor Atual',
    'Account Definition para terceiros' AS 'Descrição'

UNION ALL

SELECT 
    'Custom\AD\DefaultAccountDef' AS 'Parâmetro',
    ISNULL(
        (SELECT TOP 1 Value FROM QBMConfigParm WHERE Path = 'Custom\AD\DefaultAccountDef'),
        'NÃO CONFIGURADO'
    ) AS 'Valor Atual',
    'Account Definition padrão personalizada' AS 'Descrição';

-- =====================================================
-- 8. TEMPLATE DE CONFIGURAÇÃO GERADO AUTOMATICAMENTE
-- =====================================================
PRINT ''
PRINT '=== TEMPLATE DE CONFIGURAÇÃO GERADO ==='
PRINT 'Copie e cole este código na função GetAccountDefinitionsForDomain():'
PRINT ''

-- Gerar template automaticamente baseado nos dados
DECLARE @Template NVARCHAR(MAX) = ''
DECLARE @CurrentDomain NVARCHAR(100) = ''
DECLARE @DomainClause NVARCHAR(200) = ''
DECLARE @AccountDefLines NVARCHAR(MAX) = ''

DECLARE domain_cursor CURSOR FOR
SELECT DISTINCT d.Ident_Domain
FROM ADSDomain d
INNER JOIN ADSAccount a ON d.UID_ADSDomain = a.UID_ADSDomain AND a.XMarkedForDeletion = 0
WHERE d.XMarkedForDeletion = 0
ORDER BY d.Ident_Domain

OPEN domain_cursor
FETCH NEXT FROM domain_cursor INTO @CurrentDomain

WHILE @@FETCH_STATUS = 0
BEGIN
    -- Gerar linha Case
    SET @DomainClause = 'Case "' + UPPER(@CurrentDomain) + '", "' + UPPER(REPLACE(@CurrentDomain, '.LOCAL', '')) + '"'
    PRINT @DomainClause
    PRINT '    ''-> Account Definitions para ' + @CurrentDomain
    
    -- Gerar linhas de Account Definitions
    DECLARE accountdef_cursor CURSOR FOR
    SELECT DISTINCT tad.Ident_TSBAccountDef
    FROM ADSDomain d
    INNER JOIN ADSAccount a ON d.UID_ADSDomain = a.UID_ADSDomain AND a.XMarkedForDeletion = 0
    INNER JOIN TSBAccountDef tad ON a.UID_TSBAccountDef = tad.UID_TSBAccountDef
    WHERE d.Ident_Domain = @CurrentDomain
    ORDER BY tad.Ident_TSBAccountDef
    
    DECLARE @AccountDef NVARCHAR(100)
    OPEN accountdef_cursor
    FETCH NEXT FROM accountdef_cursor INTO @AccountDef
    
    WHILE @@FETCH_STATUS = 0
    BEGIN
        PRINT '    accountDefs.Add("' + @AccountDef + '")'
        FETCH NEXT FROM accountdef_cursor INTO @AccountDef
    END
    
    CLOSE accountdef_cursor
    DEALLOCATE accountdef_cursor
    
    PRINT ''
    FETCH NEXT FROM domain_cursor INTO @CurrentDomain
END

CLOSE domain_cursor
DEALLOCATE domain_cursor

-- =====================================================
-- 9. RESUMO EXECUTIVO
-- =====================================================
PRINT ''
PRINT '=== RESUMO EXECUTIVO ==='
SELECT 
    'Total de Domínios' AS 'Métrica',
    CAST(COUNT(DISTINCT d.UID_ADSDomain) AS VARCHAR(10)) AS 'Valor'
FROM ADSDomain d
WHERE d.XMarkedForDeletion = 0

UNION ALL

SELECT 
    'Total de Account Definitions' AS 'Métrica',
    CAST(COUNT(DISTINCT tad.UID_TSBAccountDef) AS VARCHAR(10)) AS 'Valor'
FROM TSBAccountDef tad
INNER JOIN ADSAccount a ON tad.UID_TSBAccountDef = a.UID_TSBAccountDef
WHERE tad.XMarkedForDeletion = 0 AND a.XMarkedForDeletion = 0

UNION ALL

SELECT 
    'Combinações Domínio/AccountDef' AS 'Métrica',
    CAST(COUNT(*) AS VARCHAR(10)) AS 'Valor'
FROM (
    SELECT DISTINCT d.UID_ADSDomain, tad.UID_TSBAccountDef
    FROM ADSDomain d
    INNER JOIN ADSAccount a ON d.UID_ADSDomain = a.UID_ADSDomain AND a.XMarkedForDeletion = 0
    INNER JOIN TSBAccountDef tad ON a.UID_TSBAccountDef = tad.UID_TSBAccountDef
    WHERE d.XMarkedForDeletion = 0
) AS Combinations

UNION ALL

SELECT 
    'Pessoas com Gerente em Domínio Diferente' AS 'Métrica',
    CAST(COUNT(*) AS VARCHAR(10)) AS 'Valor'
FROM (
    SELECT DISTINCT sub.UID_Person
    FROM Person sub
    INNER JOIN Person mgr ON sub.UID_PersonHead = mgr.UID_Person
    INNER JOIN ADSAccount sub_acc ON sub.UID_Person = sub_acc.UID_Person
    INNER JOIN ADSAccount mgr_acc ON mgr.UID_Person = mgr_acc.UID_Person
    WHERE sub.XMarkedForDeletion = 0
        AND mgr.XMarkedForDeletion = 0
        AND sub_acc.XMarkedForDeletion = 0
        AND mgr_acc.XMarkedForDeletion = 0
        AND sub_acc.UID_ADSDomain != mgr_acc.UID_ADSDomain
) AS CrossDomainReports;
