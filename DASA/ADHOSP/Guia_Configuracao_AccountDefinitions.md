# Guia de Configuração - Account Definitions por Domínio

## 📋 Visão Geral

A nova versão do script `CCC_TPL_ADSAccount_ObjectkeyManager_Flexible.vb` foi projetada para ser **extremamente fácil de configurar** e manter diferentes Account Definitions por domínio.

## 🎯 Principais Melhorias

### ✅ **Clareza Total**
- **Seção de configuração centralizada** - todas as Account Definitions ficam em um só lugar
- **Comentários explicativos** - cada linha está documentada
- **Estrutura visual clara** - fácil de entender o que cada domínio usa

### ✅ **Facilidade de Manutenção**
- **Adicionar novo domínio**: apenas 3-4 linhas de código
- **Modificar Account Definitions**: editar apenas uma função
- **Sem quebrar código existente**: mudanças são isoladas

### ✅ **Flexibilidade Total**
- **Account Definitions específicas por domínio**
- **Account Definitions padrão como fallback**
- **Suporte a configurações dinâmicas**
- **Integração com configurações do sistema**

## 🔧 Como Configurar Novos Domínios

### 1. **Localizar a Seção de Configuração**

Abra o arquivo e encontre esta seção:

```vb
'-> =====================================================
'-> CONFIGURAÇÃO POR DOMÍNIO - EDITE AQUI PARA ADICIONAR NOVOS DOMÍNIOS
'-> =====================================================
Select Case domainName.ToUpper()
```

### 2. **Adicionar Novo Domínio**

Adicione um novo `Case` para seu domínio:

```vb
Case "SEUDOMINIO.COM", "SEUDOMINIO"  ' <- Nome do seu domínio
    '-> Account Definitions para SEUDOMINIO
    accountDefs.Add("AD-User-SeuDominio")     ' Account Definition específica
    accountDefs.Add("AD-Standard")            ' Account Definition padrão
    accountDefs.Add("AD-Manager-SeuDominio")  ' Account Definition para gerentes
```

### 3. **Exemplo Prático**

```vb
Case "EMPRESA.LOCAL", "EMPRESA"
    '-> Account Definitions para domínio da empresa
    accountDefs.Add("AD-User")              ' Usuários padrão
    accountDefs.Add("AD-Admin")             ' Administradores
    
Case "FILIAL.LOCAL", "FILIAL"
    '-> Account Definitions para domínio da filial
    accountDefs.Add("AD-User-Filial")       ' Usuários da filial
    accountDefs.Add("AD-Terceiros")         ' Terceiros da filial
    
Case "PARCEIROS.COM", "PARCEIROS"
    '-> Account Definitions para parceiros externos
    accountDefs.Add("AD-Parceiro")          ' Conta de parceiro
    accountDefs.Add("AD-Externo")           ' Conta externa
```

## 📊 Estrutura do Código

### **Função Principal**
```vb
CCC_tpl_ADSAccount_ObjectkeyManager()
├── GetCurrentAccountDomain()           # Detecta domínio atual
├── GetAccountDefinitionsForDomain()    # ← CONFIGURAÇÃO AQUI
├── FindManagerAccount()                # Busca conta do gerente
└── Retorna resultado
```

### **Funções de Configuração**
- `GetAccountDefinitionsForDomain()` - **PRINCIPAL**: Configure aqui os domínios
- `GetDefaultAccountDefinitions()` - Configure Account Definitions padrão
- `GetConfigParameter()` - Busca configurações do sistema

### **Funções Auxiliares**
- `GetCurrentAccountDomain()` - Detecta domínio automaticamente
- `GetDomainName()` - Converte UID para nome do domínio
- `FindManagerAccount()` - Executa a busca
- `BuildAccountDefinitionFilter()` - Constrói filtros SQL

## 🚀 Exemplos de Configuração

### **Cenário 1: Empresa com 2 Domínios**

```vb
Case "MATRIZ.COM", "MATRIZ"
    accountDefs.Add("AD-User")
    accountDefs.Add("AD-Manager")
    
Case "FILIAL.COM", "FILIAL"
    accountDefs.Add("AD-User-Filial")
    accountDefs.Add("AD-Supervisor")
```

### **Cenário 2: Múltiplos Ambientes**

```vb
Case "PRODUCAO.COM", "PROD"
    accountDefs.Add("AD-User-Prod")
    accountDefs.Add("AD-Admin-Prod")
    
Case "DESENVOLVIMENTO.COM", "DEV"
    accountDefs.Add("AD-User-Dev")
    accountDefs.Add("AD-Developer")
    
Case "TESTE.COM", "TEST"
    accountDefs.Add("AD-User-Test")
    accountDefs.Add("AD-Tester")
```

### **Cenário 3: Com Configurações Dinâmicas**

```vb
Case "TERCEIROS.COM", "TERCEIROS"
    '-> Busca configuração do sistema
    Dim terceiroAccountDef As String = GetConfigParameter("Custom\AD\AccountDefforTerceiro")
    If Not String.IsNullOrEmpty(terceiroAccountDef) Then
        accountDefs.Add(terceiroAccountDef)
    End If
    accountDefs.Add("AD-Terceiros")
```

## 🔍 Como Funciona

### **1. Detecção do Domínio**
O script detecta automaticamente o domínio da conta atual através de múltiplos métodos.

### **2. Busca de Account Definitions**
Com base no domínio detectado, busca as Account Definitions específicas configuradas.

### **3. Busca do Gerente**
Procura contas do gerente **apenas no mesmo domínio** usando as Account Definitions corretas.

### **4. Fallback Inteligente**
Se não encontrar no domínio específico, usa Account Definitions padrão como fallback.

## ✅ Vantagens da Nova Estrutura

| Aspecto | Versão Anterior | Nova Versão |
|---------|----------------|-------------|
| **Configuração** | Hardcoded, difícil de alterar | Centralizada, fácil de editar |
| **Clareza** | Código misturado | Seção específica para configuração |
| **Manutenção** | Requer conhecimento técnico | Apenas editar lista de Account Definitions |
| **Flexibilidade** | Limitada a 2 Account Definitions | Ilimitada, qualquer quantidade |
| **Documentação** | Pouca | Comentários explicativos em cada seção |
| **Teste** | Difícil de testar cenários | Fácil de testar cada domínio |

## 🛠️ Próximos Passos

1. **Identifique seus domínios** e suas Account Definitions
2. **Edite a função** `GetAccountDefinitionsForDomain()`
3. **Adicione os Cases** para cada domínio
4. **Teste em ambiente de desenvolvimento**
5. **Implemente em produção**

## 📝 Template para Novos Domínios

```vb
Case "NOME_DO_DOMINIO.COM", "NOME_DO_DOMINIO"
    '-> Account Definitions para NOME_DO_DOMINIO
    accountDefs.Add("AD-User-NomeDominio")    ' Usuários padrão
    accountDefs.Add("AD-Admin-NomeDominio")   ' Administradores
    ' accountDefs.Add("Outra-AccountDef")     ' Outras conforme necessário
```

**Substitua `NOME_DO_DOMINIO` pelo nome real do seu domínio e adicione as Account Definitions apropriadas!**
