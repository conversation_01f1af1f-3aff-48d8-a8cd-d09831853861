Function CCC_tpl_ADSAccount_ObjectkeyManager(strUIDLineManager As String) As String

    Dim strResult As String = ""
    Try
        '-> Only if there is a Line Manager on person configured
        If Not String.IsNullOrEmpty(strUIDLineManager) Then
            '-> Get the domain of the current account
            Dim strCurrentAccountDomain As String = GetCurrentAccountDomain()
            
            Dim ColADAccounts As IEntityCollection
            Dim qADSAccount As Query

            '-> Build the query based on whether we have domain information
            If Not String.IsNullOrEmpty(strCurrentAccountDomain) Then
                '-> PREFERRED: Search for ANY manager account in the same domain
                qADSAccount = Query.From("ADSAccount") _
                              .Where(String.Format("uid_person = '{0}' and UID_ADSDomain = '{1}'", strUIDLineManager, strCurrentAccountDomain)) _
                              .Select("xobjectkey")
            Else
                '-> FALLBACK: If current domain is unknown, get any manager account
                qADSAccount = Query.From("ADSAccount") _
                              .Where(String.Format("uid_person = '{0}'", strUIDLineManager)) _
                              .Select("xobjectkey")
            End If

            ColADAccounts = Session.Source.GetCollection(qADSAccount)
            
            '-> Use the first manager account found (if any)
            If ColADAccounts.Count > 0 Then
                strResult = ColADAccounts(0).GetValue("xobjectkey").String
            End If
            '-> If no manager account found, strResult remains empty (as requested)
        End If
    Catch ex As Exception
        Throw New Exception("Error in script CCC_tpl_ADSAccount_ObjectkeyManager: " & ex.Message, ex)
    End Try
    
    Return strResult

End Function

Private Function GetCurrentAccountDomain() As String
    ' Method 1: From Variables (template context)
    Try
        Dim domainValue As Object = Variables("UID_ADSDomain")
        If domainValue IsNot Nothing AndAlso Not String.IsNullOrEmpty(domainValue.ToString()) Then
            Return domainValue.ToString()
        End If
    Catch
        ' Continue to next method
    End Try
    
    ' Method 2: From Entity (if available)
    Try
        Dim entityValue As Object = Entity.GetValue("UID_ADSDomain")
        If entityValue IsNot Nothing AndAlso Not String.IsNullOrEmpty(entityValue.ToString()) Then
            Return entityValue.ToString()
        End If
    Catch
        ' Continue to next method
    End Try
    
    ' Method 3: From current person's existing AD accounts
    Try
        Dim personValue As Object = Variables("UID_Person")
        If personValue IsNot Nothing AndAlso Not String.IsNullOrEmpty(personValue.ToString()) Then
            Dim strCurrentPerson As String = personValue.ToString()
            Dim qCurrentDomain As Query = Query.From("ADSAccount") _
                                .Where(String.Format("uid_person = '{0}'", strCurrentPerson)) _
                                .Select("UID_ADSDomain")
            Dim colCurrentDomain As IEntityCollection = Session.Source.GetCollection(qCurrentDomain)
            If colCurrentDomain.Count > 0 Then
                Return colCurrentDomain(0).GetValue("UID_ADSDomain").String
            End If
        End If
    Catch
        ' All methods failed
    End Try
    
    Return "" ' Return empty string if domain cannot be determined
End Function
