{"name": "Create and Assign Work Email", "description": "This workflow creates and assigns work emails for new employees by concatenating their first name and last name with the domain @sec4you.com.", "modified": "2024-07-26T19:08:05.568052991Z", "modifiedBy": {"type": "IDENTITY", "id": "1c42bb8b0b444ecaa5191188c8fcac32", "name": "pedro.alcantara"}, "definition": {"start": "Generate Email", "steps": {"End Workflow - Success": {"type": "success"}, "Generate Email": {"actionId": "sp:transform", "attributes": {"expression": "$.trigger.identity.firstname.toLowerCase() + '.' + $.trigger.identity.lastname.toLowerCase() + '@sec4you.com'", "resultName": "email"}, "description": "This action generates the work email by concatenating first name, last name, and the domain.", "nextStep": "Assign <PERSON><PERSON>", "type": "action", "versionNumber": 1}, "Assign Email": {"actionId": "sp:access:manage", "attributes": {"comments": "Automatically assigning generated work email to the new employee.", "addIdentity.$": "$.trigger.identity.id", "requestType": "ASSIGN_EMAIL", "requestedItems": [{"name": "{{$.steps.Generate Email.result.email}}", "type": "EMAIL"}]}, "description": "This action assigns the generated work email to the new employee.", "nextStep": "End Workflow - Success", "type": "action", "versionNumber": 1}}}, "creator": {"type": "IDENTITY", "id": "1c42bb8b0b444ecaa5191188c8fcac32", "name": "pedro.alcantara"}, "trigger": {"type": "EVENT", "attributes": {"id": "idn:new-employee-onboarded"}}}