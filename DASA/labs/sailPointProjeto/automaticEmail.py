def check_new_employees():
    conn = mysql.connector.connect(
        host="*************",
        port=3306,  # Definindo a porta separadamente
        user="orangehrm_user",
        password="qwe123QWE!@#",
        database="orangehrm_db"
    )
    cursor = conn.cursor()
    query = "SELECT emp_number, emp_firstname, emp_lastname FROM hs_hr_employee WHERE emp_work_email IS NULL"
    cursor.execute(query)
    employees = cursor.fetchall()
    cursor.close()
    conn.close()
    print("Employees fetched:", employees)  # Adicionado para verificação
    return employees

