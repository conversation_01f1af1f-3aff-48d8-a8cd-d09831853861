﻿2024-10-10 23:25:09 -03:00 - Info - Indexing existing data...
2024-10-10 23:25:09 -03:00 - Serious -     Duplicate key: 
2024-10-10 23:25:09 -03:00 - Serious -     Duplicate key: 
2024-10-10 23:25:09 -03:00 - Serious -     Duplicate key: 
2024-10-10 23:25:09 -03:00 - Serious -     Duplicate key: 
2024-10-10 23:25:09 -03:00 - Serious -     Duplicate key: 
2024-10-10 23:25:09 -03:00 - Serious -     Duplicate key: 
2024-10-10 23:25:09 -03:00 - Serious -     Duplicate key: 
2024-10-10 23:25:09 -03:00 - Serious -     Duplicate key: 
2024-10-10 23:25:09 -03:00 - Serious -     Duplicate key: 
2024-10-10 23:25:09 -03:00 - Serious -     Duplicate key: 
2024-10-10 23:25:09 -03:00 - Serious -     Duplicate key: 
2024-10-10 23:25:09 -03:00 - Serious -     Duplicate key: 
2024-10-10 23:25:09 -03:00 - Serious -     Duplicate key: 
2024-10-10 23:25:09 -03:00 - Serious -     Duplicate key: 
2024-10-10 23:25:09 -03:00 - Serious -     Duplicate key: 
2024-10-10 23:25:09 -03:00 - Serious -     Duplicate key: 
2024-10-10 23:25:09 -03:00 - Serious -     Duplicate key: 
2024-10-10 23:25:09 -03:00 - Serious -     Duplicate key: 
2024-10-10 23:25:09 -03:00 - Serious -     Duplicate key: 
2024-10-10 23:25:09 -03:00 - Serious -     Duplicate key: 
2024-10-10 23:25:09 -03:00 - Serious -     Duplicate key: 
2024-10-10 23:25:09 -03:00 - Serious -     Duplicate key: 
2024-10-10 23:25:09 -03:00 - Serious -     Duplicate key: 
2024-10-10 23:25:09 -03:00 - Serious -     Duplicate key: 
2024-10-10 23:25:09 -03:00 - Serious -     Duplicate key: 
2024-10-10 23:25:09 -03:00 - Info - Import data from file
2024-10-10 23:25:13 -03:00 - Serious -     Error in line 18613: [810457] Error saving Person Daiana Ribeiro Sobral (T33553019816)
[810306] Error running 'OnGenerate' in logic module 'VI.DB.Entities.EventsEntityLogic'.
[810103] Error generating processes for event Update.
[810222] Error running script 'Event_Update'.
[810108] Error generation process step event UPDATE.
[810109] Error generating process Process SUB-IDENTITY Conta S - Update Info.
[810155] Error running prescript.
[810048] Error getting single property Person.UID_Person.
[810391] Error applying changes to database.
[810076] Identities: Value in field Personnel number must be unique. Object Daiana Ribeiro Sobral (F33553019816) already has this value (92160).
2024-10-10 23:25:13 -03:00 - Serious -     Line data was: "92160";"Daiana";"Ribeiro Sobral";"2";"";"Daiana Ribeiro Sobral";"Fisioterapeuta I";"2";"1986-05-09 00:00:00";"2";"50007218";"I8856014";"2019-02-04 00:00:00";"9999-12-31 00:00:00";"Hospital Leforte S.A";"92981";"0335";"BR";"";"";"<EMAIL>";"Leforte Morumbi";"335.530.198-16";"436796156";"Op Técnicos";"Analista Técnico";"Mercado Hospit DASA";"São Paulo";"2024-06-18 19:58:25";"1"
2024-10-10 23:25:13 -03:00 - Serious -     Error in line 29424: [810457] Error saving Person Mariana Costa Cruz (T41007189851)
[810306] Error running 'OnGenerate' in logic module 'VI.DB.Entities.EventsEntityLogic'.
[810103] Error generating processes for event Update.
[810222] Error running script 'Event_Update'.
[810108] Error generation process step event UPDATE.
[810109] Error generating process Process SUB-IDENTITY Conta S - Update Info.
[810155] Error running prescript.
[810048] Error getting single property Person.UID_Person.
[810391] Error applying changes to database.
[810076] Identities: Value in field Personnel number must be unique. Object Mariana Costa Cruz (F41007189851) already has this value (141852).
2024-10-10 23:25:13 -03:00 - Serious -     Line data was: "141852";"Mariana";"Costa Cruz";"2";"";"Mariana Costa Cruz";"Analista Compliance III";"2";"1991-07-29 00:00:00";"0";"50009365";"A09027D896";"2024-10-03 00:00:00";"9999-12-31 00:00:00";"Diagnósticos da AméricaSA";"35879";"0065";"BR";"(14) 9973-6911";"(14)997369111";"<EMAIL>";"Dasa";"410.071.898-51";"47974922X";"Op Administrativos";"Analista";"Jurídico";"São Paulo";"2024-10-02 08:08:59";"1"
2024-10-10 23:25:14 -03:00 - Serious -     Error in line 43763: [810457] Error saving Person Gabriel Broad Enedino Fernandes (T18371092733)
[810306] Error running 'OnGenerate' in logic module 'VI.DB.Entities.EventsEntityLogic'.
[810103] Error generating processes for event Update.
[810222] Error running script 'Event_Update'.
[810108] Error generation process step event UPDATE.
[810109] Error generating process Process SUB-IDENTITY Conta S - Update Info.
[810155] Error running prescript.
[810048] Error getting single property Person.UID_Person.
[810391] Error applying changes to database.
[810076] Identities: Value in field Personnel number must be unique. Object Gabriel Broad Florencio Enedino (F18371092733) already has this value (86823).
2024-10-10 23:25:14 -03:00 - Serious -     Line data was: "86823";"Gabriel";"Broad Florencio Enedino";"1";"";"Gabriel Broad Florencio Enedino";"Assistente Coleta Atendimento";"1";"1998-11-11 00:00:00";"0";"50008744";"U01011D198";"2022-02-07 00:00:00";"9999-12-31 00:00:00";"Diagnósticos da AméricaSA";"53694";"0053";"BR";"(21) 2542-1261";"(21)988690585";"<EMAIL>";"Bronstein";"183.710.927-33";"267370344";"Op Técnicos";"Assistente Técnico";"Regional RJ";"Rio de Janeiro";"2024-09-30 20:25:39";"1"
2024-10-10 23:25:14 -03:00 - Info - Results
2024-10-10 23:25:14 -03:00 - Info -     50606 lines imported
2024-10-10 23:25:14 -03:00 - Info -     0 header lines
2024-10-10 23:25:14 -03:00 - Info -     0 inserted
2024-10-10 23:25:14 -03:00 - Info -     0 changed
2024-10-10 23:25:14 -03:00 - Info -     0 deleted
2024-10-10 23:25:14 -03:00 - Info -     0 not changed
2024-10-10 23:25:14 -03:00 - Info -     50603 not found
2024-10-10 23:25:14 -03:00 - Info -     0 empty lines
2024-10-10 23:25:14 -03:00 - Info -     3 errors
