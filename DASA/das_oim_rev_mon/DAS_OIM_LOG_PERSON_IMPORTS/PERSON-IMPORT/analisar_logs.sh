#!/bin/bash

# Nome do arquivo de saída CSV
output_file="analise_consolidada.csv"

# Cabeçalho do CSV
echo "Data,Total Linhas Importadas,Total Linhas Inseridas,Total Erros" > "$output_file"

# Array associativo para armazenar os totais por dia
declare -A total_imported
declare -A total_inserted
declare -A total_errors

# Processar cada arquivo .log no diretório atual
for logfile in Person_Import_*.log; do
    # Extrair data do nome do arquivo (formato YYYYMMDD)
    filedate=$(echo "$logfile" | grep -oE '[0-9]{8}')
    
    # Converter para formato YYYY-MM-DD (chave do array)
    formatted_date=$(date -d "$filedate" +"%Y-%m-%d" 2>/dev/null)
    
    # Extrair informações do conteúdo do arquivo
    lines_imported=$(grep -E "[0-9]+ lines imported" "$logfile" | awk '{print $7}')
    inserted=$(grep -E "[0-9]+ inserted" "$logfile" | awk '{print $7}')
    errors=$(grep -E "[0-9]+ errors" "$logfile" | awk '{print $7}')
    
    # Definir 0 se vazio
    lines_imported=${lines_imported:-0}
    inserted=${inserted:-0}
    errors=${errors:-0}
    
    # Somar aos totais do dia
    total_imported[$formatted_date]=$(( ${total_imported[$formatted_date]:-0} + lines_imported ))
    total_inserted[$formatted_date]=$(( ${total_inserted[$formatted_date]:-0} + inserted ))
    total_errors[$formatted_date]=$(( ${total_errors[$formatted_date]:-0} + errors ))
done

# Ordenar as datas cronologicamente
sorted_dates=$(printf "%s\n" "${!total_imported[@]}" | sort)

# Escrever os resultados no CSV
for date in $sorted_dates; do
    echo "$date,${total_imported[$date]},${total_inserted[$date]},${total_errors[$date]}" >> "$output_file"
done

echo "Análise consolidada por dia concluída. Resultados salvos em $output_file"