SELECT 
    ad.samaccountname AS conta_AD,
    ad.UID_Person,
    p.centralaccount AS login,
    CASE 
        WHEN p.isinactive = '0' THEN 'Ativo'
        ELSE 'Inativo'
    END AS status
FROM 
    ADSAccount ad
JOIN 
    Person p ON ad.UID_Person = p.UID_Person
WHERE 
    -- Filtra para credenciais que começam com F, T ou M
    LEFT(p.centralaccount, 1) IN ('F', 'T', 'M')
    -- Garante que o segundo caractere é um número para evitar casos como "TESTE"
    AND SUBSTRING(p.centralaccount, 2, 1) LIKE '[0-9]'
    AND (
        -- Exclui credenciais que têm exatamente 11 números após a letra inicial
        LEN(p.centralaccount) <> 12
        OR ISNUMERIC(SUBSTRING(p.centralaccount, 2, 11)) = 0
    )
GROUP BY 
    ad.samaccountname, ad.UID_Person, p.centralaccount, p.isinactive
ORDER BY 
    p.centralaccount;
