SELECT 
    p.UID_Person,
    p.centralaccount AS login,
    p.preferredname AS nome_completo,
    CASE 
        WHEN p.isinactive = '0' THEN 'Ativo'
        ELSE 'Inativo'
    END AS status
FROM 
    Person p
JOIN 
    ADSAccount ad ON ad.UID_Person = p.UID_Person
WHERE 
    -- Filtra para credenciais que começam com F, T ou M
    LEFT(p.centralaccount, 1) IN ('F', 'T', 'M')
    AND 
    -- Garante que o segundo caractere é um número para evitar casos como "TESTE"
    SUBSTRING(p.centralaccount, 2, 1) LIKE '[0-9]'
    AND (
        -- Exclui credenciais que têm exatamente 11 números após a letra inicial
        LEN(p.centralaccount) <> 12
        OR ISNUMERIC(SUBSTRING(p.centralaccount, 2, 11)) = 0
    )
GROUP BY 
    p.UID_Person, p.centralaccount, p.preferredname, p.isinactive;
