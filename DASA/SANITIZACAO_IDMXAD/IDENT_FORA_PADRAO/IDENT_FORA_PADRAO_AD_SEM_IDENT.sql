SELECT 
    ad.samaccountname AS conta_AD,
    ad.UID_ADSAccount
FROM 
    ADSAccount ad
WHERE 
    -- Contas sem identidade associada (UID_Person é NULL)
    ad.UID_Person IS NULL
    -- Filtra para credenciais que começam com F, T ou M
    AND LEFT(ad.samaccountname, 1) IN ('F', 'T', 'M')
    -- <PERSON><PERSON><PERSON> que o segundo caractere é um número para evitar casos como "TESTE"
    AND SUBSTRING(ad.samaccountname, 2, 1) LIKE '[0-9]'
    -- Filtros para credenciais fora do padrão
    AND (
        -- Comprimento diferente de 12 caracteres (não 1 letra + 11 números)
        LEN(ad.samaccountname) <> 12
        OR ISNUMERIC(SUBSTRING(ad.samaccountname, 2, 11)) = 0
    )
ORDER BY 
    ad.samaccountname;
