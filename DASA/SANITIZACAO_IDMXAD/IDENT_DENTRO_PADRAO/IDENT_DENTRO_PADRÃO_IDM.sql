SELECT 
    p.UID_Person,
    p.centralaccount AS login,
    p.preferredname AS nome_completo,
    CASE 
        WHEN p.isinactive = '0' THEN 'Ativo'
        ELSE 'Inativo'
    END AS status
FROM 
    Person p
JOIN 
    ADSAccount ad ON ad.UID_Person = p.UID_Person
WHERE 
    -- Verifica o padrão: começa com F, T ou M, seguido por exatamente 11 números
    (p.centralaccount LIKE 'F%' OR p.centralaccount LIKE 'T%' OR p.centralaccount LIKE 'M%')
    AND LEN(p.centralaccount) = 12
    AND ISNUMERIC(SUBSTRING(p.centralaccount, 2, 11)) = 1
GROUP BY 
    p.UID_Person, p.centralaccount, p.preferredname, p.isinactive;
