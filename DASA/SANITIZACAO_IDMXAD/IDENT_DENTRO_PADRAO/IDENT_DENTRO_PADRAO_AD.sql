SELECT 
    ad.samaccountname AS conta_AD,
    ad.UID_Person,
    p.centralaccount AS login,
    CASE 
        WHEN p.isinactive = '0' THEN 'Ativo'
        ELSE 'Inativo'
    END AS status
FROM 
    ADSAccount ad
JOIN 
    Person p ON ad.UID_Person = p.UID_Person
WHERE 
    -- Filtra para credenciais dentro do padrão
    LEFT(p.centralaccount, 1) IN ('F', 'T', 'M')
    AND LEN(p.centralaccount) = 12
    AND ISNUMERIC(SUBSTRING(p.centralaccount, 2, 11)) = 1
GROUP BY 
    ad.samaccountname, ad.UID_Person, p.centralaccount, p.isinactive
ORDER BY 
    p.centralaccount;
