SELECT 
    p.UID_Person,
    p.centralaccount AS login_principal,
    p.preferredname AS nome_completo,
    CASE 
        WHEN p.isinactive = '0' THEN 'Ativo'
        ELSE 'Inativo'
    END AS status_identidade,
    COUNT(ad.samaccountname) AS total_contas_AD,
    STRING_AGG(ad.samaccountname, ', ') AS contas_AD,
    CASE 
        WHEN COUNT(DISTINCT LEFT(ad.samaccountname, 1)) > 1 THEN 'Divergência de Prefixo'
        ELSE 'Único Prefixo'
    END AS prefixo_divergencia,
    CASE 
        WHEN COUNT(DISTINCT CASE WHEN ad.AccountDisabled = 0 THEN 'Ativo' ELSE 'Inativo' END) > 1 THEN 'Divergência de Status no AD'
        ELSE 'Status Consistente no AD'
    END AS status_divergencia_AD,
    CASE 
        WHEN (p.isinactive = '0' AND SUM(CAST(ad.AccountDisabled AS INT)) = COUNT(ad.AccountDisabled)) -- To<PERSON> as contas AD estão inativas
             OR (p.isinactive = '1' AND SUM(CAST(ad.AccountDisabled AS INT)) < COUNT(ad.AccountDisabled)) -- Pelo menos uma conta AD está ativa
        THEN 'Divergência entre IDM e AD'
        ELSE 'Consistência entre IDM e AD'
    END AS consistencia_idm_ad,
    CASE 
        WHEN COUNT(CASE WHEN ad.AccountDisabled = 0 AND ad.samaccountname = p.centralaccount THEN 1 END) = 1 
             AND COUNT(CASE WHEN ad.AccountDisabled = 1 AND ad.samaccountname <> p.centralaccount THEN 1 END) >= 1 
        THEN 'Conta Ativa Principal, Conta Inativa Diferente'
        ELSE 'Outros'
    END AS situacao_conta
FROM 
    Person p
JOIN 
    ADSAccount ad ON ad.UID_Person = p.UID_Person
WHERE 
    -- Filtra para credenciais dentro do padrão: começam com F, T ou M e têm 11 números após a letra
    LEFT(p.centralaccount, 1) IN ('F', 'T', 'M')
    AND LEN(p.centralaccount) = 12
    AND ISNUMERIC(SUBSTRING(p.centralaccount, 2, 11)) = 1
GROUP BY 
    p.UID_Person, p.centralaccount, p.preferredname, p.isinactive
HAVING 
    -- Condição para exibir identidades com múltiplos prefixos de contas no AD ou status divergente entre IDM e AD
    COUNT(DISTINCT LEFT(ad.samaccountname, 1)) > 1  -- Mais de um prefixo de conta no AD (F, T, M)
    OR COUNT(DISTINCT CASE WHEN ad.AccountDisabled = 0 THEN 'Ativo' ELSE 'Inativo' END) > 1  -- Divergência de status entre contas no AD
    OR ((p.isinactive = '0' AND SUM(CAST(ad.AccountDisabled AS INT)) = COUNT(ad.AccountDisabled))  -- Divergência de status entre IDM e AD
        OR (p.isinactive = '1' AND SUM(CAST(ad.AccountDisabled AS INT)) < COUNT(ad.AccountDisabled)))
ORDER BY 
    p.centralaccount;
